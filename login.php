<?php
$start = microtime(true);
require_once 'includes/db.php';
require_once 'includes/auth.php';
$after_require = microtime(true);

header('Content-Type: application/json');
$data = json_decode(file_get_contents('php://input'), true);
$after_input = microtime(true);
if (!$data || !isset($data['email'], $data['name'], $data['google_id'])) {
    echo json_encode(['success' => false, 'error' => 'Invalid data']);
    exit;
}
$email = $data['email'];
$name = $data['name'];
$google_id = $data['google_id'];
$role = $data['role'] ?? 'customer'; // Allow role selection, default to customer

$stmt = $pdo->prepare('SELECT * FROM users WHERE email = ? LIMIT 1');
$stmt->execute([$email]);
$user = $stmt->fetch();
$after_select = microtime(true);

if (!$user) {
    // Insert new user with selected role
    $default_password = password_hash($google_id, PASSWORD_DEFAULT); // Use Google ID as password
    $profile_completed = ($role === 'reviewer') ? 0 : 1; // Reviewers need to complete profile
    $stmt = $pdo->prepare('INSERT INTO users (name, email, password, role, wallet_balance, profile_completed) VALUES (?, ?, ?, ?, ?, ?)');
    $stmt->execute([$name, $email, $default_password, $role, 100.00, $profile_completed]); // Give new users $100
    $user_id = $pdo->lastInsertId();
    $user = [
        'id' => $user_id,
        'name' => $name,
        'email' => $email,
        'role' => $role,
        'profile_completed' => $profile_completed
    ];
} else {
    // Existing user - update their role if they want to change it
    if ($role !== $user['role']) {
        $stmt = $pdo->prepare('UPDATE users SET role = ? WHERE id = ?');
        $stmt->execute([$role, $user['id']]);
        $user['role'] = $role;
    }
    $user_id = $user['id'];
}
$after_user = microtime(true);

// Set session
$_SESSION['user_id'] = $user_id;
$_SESSION['role'] = $user['role'];
// Also cache user data for performance
$_SESSION['user_data'] = $user;
$after_session = microtime(true);

// Redirect based on selected role (not stored role)
if ($role === 'admin') $redirect = '/dashboard/admin.php';
elseif ($role === 'reviewer') {
    // Check if reviewer profile is completed
    if ($user['profile_completed']) {
        $redirect = '/dashboard/reviewer.php';
    } else {
        $redirect = '/dashboard/reviewer_profile_setup.php';
    }
} else $redirect = '/dashboard/customer.php';
$after_redirect = microtime(true);

error_log('login.php timing: require=' . ($after_require - $start) . ' input=' . ($after_input - $after_require) . ' select=' . ($after_select - $after_input) . ' user=' . ($after_user - $after_select) . ' session=' . ($after_session - $after_user) . ' redirect=' . ($after_redirect - $after_session) . ' total=' . ($after_redirect - $start));

echo json_encode(['success' => true, 'redirect' => $redirect]); 