<?php
// google_login.php - Handle Google OAuth login

// Start session only if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once 'includes/db.php';

// Import only necessary functions instead of full auth.php to avoid header conflicts
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function check_rate_limit($action, $max_attempts = 5, $time_window = 300) {
    $ip = $_SERVER['REMOTE_ADDR'];
    $key = "rate_limit_{$action}_{$ip}";
    
    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = ['attempts' => 0, 'first_attempt' => time()];
    }
    
    $rate_data = $_SESSION[$key];
    
    if (time() - $rate_data['first_attempt'] > $time_window) {
        $_SESSION[$key] = ['attempts' => 1, 'first_attempt' => time()];
        return true;
    }
    
    if ($rate_data['attempts'] >= $max_attempts) {
        return false;
    }
    
    $_SESSION[$key]['attempts']++;
    return true;
}

// Set JSON response header
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Check rate limiting for login attempts
if (!check_rate_limit('login', 10, 300)) {
    http_response_code(429);
    echo json_encode(['error' => 'Too many login attempts. Please try again later.']);
    exit;
}

// Verify CSRF token if present
if (isset($_POST['csrf_token']) && !verify_csrf_token($_POST['csrf_token'])) {
    http_response_code(403);
    echo json_encode(['error' => 'Invalid security token.']);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);
if (!$input) {
    $input = $_POST;
}

$email = sanitize_input($input['email'] ?? '');
$name = sanitize_input($input['name'] ?? '');
$google_id = sanitize_input($input['google_id'] ?? '');
$role = sanitize_input($input['role'] ?? 'customer');

// Validate inputs
if (empty($email) || !validate_email($email)) {
    http_response_code(400);
    echo json_encode(['error' => 'Valid email is required.']);
    exit;
}

if (empty($name)) {
    http_response_code(400);
    echo json_encode(['error' => 'Name is required.']);
    exit;
}

if (!in_array($role, ['customer', 'reviewer'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid role selected.']);
    exit;
}

try {
    $pdo = get_db();
    
    // Check if user exists by email or google_id
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? OR (google_id = ? AND google_id != '')");
    $stmt->execute([$email, $google_id]);
    $user = $stmt->fetch();
    
    if ($user) {
        // Update existing user with Google ID if not set
        if (empty($user['google_id']) && !empty($google_id)) {
            $stmt = $pdo->prepare("UPDATE users SET google_id = ?, name = ? WHERE id = ?");
            $stmt->execute([$google_id, $name, $user['id']]);
        }
        
        // Update name if different
        if ($user['name'] !== $name) {
            $stmt = $pdo->prepare("UPDATE users SET name = ? WHERE id = ?");
            $stmt->execute([$name, $user['id']]);
        }
        
        // Update role if different from what's stored (allow role switching)
        if ($user['role'] !== $role) {
            $stmt = $pdo->prepare("UPDATE users SET role = ? WHERE id = ?");
            $stmt->execute([$role, $user['id']]);
            error_log("User {$user['email']} switched role from {$user['role']} to {$role}");
        }
        
        // Get updated user data
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$user['id']]);
        $user = $stmt->fetch();
        
        // Set session data properly
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['role'] = $role; // Use the selected role, not the old database role
        $_SESSION['user_data'] = $user;
        
        $redirect_url = '/dashboard/' . $role . '.php'; // Use selected role for redirect
        
        // Force session save before response
        session_write_close();
        
        echo json_encode([
            'success' => true,
            'redirect' => $redirect_url,
            'message' => 'Login successful!',
            'user_role' => $role // Return the selected role
        ]);
        
    } else {
        // Create new user
        $stmt = $pdo->prepare("INSERT INTO users (name, email, google_id, role, created_at) VALUES (?, ?, ?, ?, NOW())");
        $stmt->execute([$name, $email, $google_id, $role]);
        
        $user_id = $pdo->lastInsertId();
        
        // Give signup bonus to new user
        require_once 'includes/wallet.php';
        give_signup_bonus($user_id, $role);
        
        // Get the newly created user with updated balance
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch();
        
        // Set session data properly
        $_SESSION['user_id'] = $user_id;
        $_SESSION['role'] = $role;
        $_SESSION['user_data'] = $user;
        
        $redirect_url = '/dashboard/' . $role . '.php';
        
        // Force session save before response
        session_write_close();
        
        echo json_encode([
            'success' => true,
            'redirect' => $redirect_url,
            'message' => 'Account created successfully!',
            'user_role' => $role
        ]);
    }
    
} catch (Exception $e) {
    error_log("Google login error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'An error occurred during login. Please try again.']);
}
?> 