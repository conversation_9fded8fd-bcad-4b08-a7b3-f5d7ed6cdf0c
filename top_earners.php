<?php
// Fetch top earners from the database
$top_earners = [];
try {
    $pdo = get_db();
    if ($pdo) {
        $stmt = $pdo->prepare("
            SELECT 
                u.id,
                u.name,
                u.profile_picture,
                COALESCE(SUM(wt.amount), 0) as weekly_earnings,
                COUNT(r.id) as reviews_completed
            FROM users u
            LEFT JOIN wallet_transactions wt ON u.id = wt.user_id 
                AND wt.type = 'review_payment' 
                AND wt.timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            LEFT JOIN reviews r ON u.id = r.reviewer_id 
                AND r.status = 'completed' 
                AND r.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            WHERE u.role = 'reviewer' 
                AND u.status = 'active'
            GROUP BY u.id, u.name, u.profile_picture
            ORDER BY weekly_earnings DESC, reviews_completed DESC
            LIMIT 5
        ");
        $stmt->execute();
        $top_earners = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (Exception $e) {
    error_log("Failed to fetch top earners: " . $e->getMessage());
}

// Fallback data if database error or no data
if (empty($top_earners)) {
    $top_earners = [
        ['name' => '<PERSON>', 'weekly_earnings' => 1250.00, 'reviews_completed' => 25, 'profile_picture' => null],
        ['name' => 'Mike Chen', 'weekly_earnings' => 980.00, 'reviews_completed' => 19, 'profile_picture' => null],
        ['name' => 'Emma Davis', 'weekly_earnings' => 875.00, 'reviews_completed' => 17, 'profile_picture' => null],
        ['name' => 'Alex Rodriguez', 'weekly_earnings' => 720.00, 'reviews_completed' => 14, 'profile_picture' => null],
        ['name' => 'Lisa Wang', 'weekly_earnings' => 650.00, 'reviews_completed' => 13, 'profile_picture' => null]
    ];
}
?>

<!-- Top Earners Section -->
<section class="top-earners-section py-5 py-md-6" id="top-earners">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <div class="section-badge mb-3">
                    <i class="fas fa-trophy me-2"></i>
                    <span>Top Earners</span>
                </div>
                <h2 class="section-title mb-4">
                    Top Earners of the 
                    <span class="text-primary">Week</span>
                </h2>
                <p class="section-description">
                    Meet our highest-earning reviewers who are making the most of their review opportunities.
                </p>
            </div>
        </div>
        
        <div class="row g-4 justify-content-center">
            <?php foreach ($top_earners as $index => $earner): ?>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="earner-card rounded-xl p-4 shadow-md hover:scale-105 transition-all duration-300 bg-white text-center position-relative">
                    <?php if ($index < 3): ?>
                    <div class="position-absolute top-0 start-50 translate-middle-x">
                        <div class="rank-badge <?php echo $index === 0 ? 'bg-warning' : ($index === 1 ? 'bg-secondary' : 'bg-bronze'); ?> text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 30px; height: 30px; font-size: 12px; font-weight: bold;">
                            <?php echo $index + 1; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="earner-avatar mb-3">
                        <?php if ($earner['profile_picture']): ?>
                            <img src="<?php echo htmlspecialchars($earner['profile_picture']); ?>" alt="<?php echo htmlspecialchars($earner['name']); ?>" class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;">
                        <?php else: ?>
                            <div class="avatar-placeholder rounded-circle bg-primary d-flex align-items-center justify-content-center mx-auto" style="width: 60px; height: 60px;">
                                <i class="fas fa-user text-white fa-lg"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <h6 class="earner-name mb-2 fw-bold"><?php echo htmlspecialchars($earner['name']); ?></h6>
                    
                    <div class="earner-stats">
                        <div class="earnings-amount text-success fw-bold mb-1">
                            ₹<?php echo number_format($earner['weekly_earnings'], 0); ?>
                        </div>
                        <div class="earnings-label text-muted small mb-2">This Week</div>
                        
                        <div class="reviews-count text-muted small">
                            <i class="fas fa-star me-1"></i>
                            <?php echo $earner['reviews_completed']; ?> reviews
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="row mt-4">
            <div class="col-12 text-center">
                <button class="btn btn-primary btn-lg" onclick="showLoginModal('reviewer')">
                    <i class="fas fa-star me-2"></i>Start Earning Today
                </button>
            </div>
        </div>
    </div>
</section>

<style>
.bg-bronze {
    background-color: #cd7f32 !important;
}

.earner-card {
    border: 1px solid #e5e7eb;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.earner-card:hover {
    border-color: #1877f2;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.rank-badge {
    z-index: 10;
    margin-top: -15px;
}

.earnings-amount {
    font-size: 1.25rem;
}

@media (max-width: 768px) {
    .earner-card {
        min-height: 180px;
    }
    
    .earnings-amount {
        font-size: 1.1rem;
    }
}
</style> 