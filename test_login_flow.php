<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Flow Test - 1xreviews</title>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .test-container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .role-selector { margin: 15px 0; }
        .role-btn { padding: 8px 16px; margin: 5px; border: 2px solid #ccc; background: white; cursor: pointer; }
        .role-btn.active { border-color: #007bff; background: #007bff; color: white; }
        #log { background: #f8f9fa; border: 1px solid #ddd; padding: 10px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Login Flow Test</h1>
        
        <div class="test-section info">
            <h3>Current Status</h3>
            <p id="statusText">Ready to test login flow</p>
        </div>
        
        <div class="test-section">
            <h3>Role Selection</h3>
            <div class="role-selector">
                <button class="role-btn active" onclick="selectRole('customer')" id="customerBtn">Customer</button>
                <button class="role-btn" onclick="selectRole('reviewer')" id="reviewerBtn">Reviewer</button>
            </div>
            <p>Selected Role: <span id="selectedRole">customer</span></p>
        </div>
        
        <div class="test-section">
            <h3>Google Login Test</h3>
            <div id="g_id_onload"
                data-client_id="136943631858-pcct72uur0r1mmk4jj0fc4d00kb8cuor.apps.googleusercontent.com"
                data-callback="handleGoogleLogin"
                data-auto_prompt="false"
                data-ux_mode="popup">
            </div>
            
            <div style="margin: 15px 0;">
                <div id="g_id_signin" class="g_id_signin" 
                    data-type="standard" 
                    data-size="large" 
                    data-theme="outline" 
                    data-text="sign_in_with" 
                    data-shape="rectangular" 
                    data-logo_alignment="left">
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Manual Login Test</h3>
            <button class="btn-primary" onclick="testLogin('customer')">Test Customer Login</button>
            <button class="btn-success" onclick="testLogin('reviewer')">Test Reviewer Login</button>
        </div>
        
        <div class="test-section">
            <h3>Debug Log</h3>
            <div id="log"></div>
            <button onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script>
        let selectedRole = 'customer';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function selectRole(role) {
            selectedRole = role;
            document.getElementById('selectedRole').textContent = role;
            
            // Update button states
            document.querySelectorAll('.role-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(role + 'Btn').classList.add('active');
            
            log(`Role selected: ${role}`);
        }
        
        function handleGoogleLogin(response) {
            log('Google login initiated...');
            
            if (response.credential) {
                try {
                    const payload = JSON.parse(atob(response.credential.split('.')[1]));
                    log(`Google payload received for: ${payload.email}`);
                    
                    const userData = {
                        email: payload.email,
                        name: payload.name,
                        picture: payload.picture,
                        role: selectedRole,
                        google_id: payload.sub
                    };
                    
                    log(`Sending login request for role: ${selectedRole}`);
                    document.getElementById('statusText').textContent = 'Logging in...';
                    
                    fetch('/google_login.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify(userData)
                    })
                    .then(async response => {
                        log(`Server response status: ${response.status}`);
                        const text = await response.text();
                        log(`Raw response: ${text.substring(0, 200)}...`);
                        
                        try {
                            const data = JSON.parse(text);
                            
                            if (data.success && data.redirect) {
                                log(`✅ Login successful! Redirecting to: ${data.redirect}`);
                                document.getElementById('statusText').textContent = 'Login successful! Redirecting...';
                                setTimeout(() => {
                                    window.location.href = data.redirect;
                                }, 1000);
                            } else {
                                log(`❌ Login failed: ${data.message || data.error}`);
                                document.getElementById('statusText').textContent = 'Login failed: ' + (data.message || data.error);
                            }
                        } catch (e) {
                            log(`❌ JSON parse error: ${e.message}`);
                            log(`Raw response was: ${text}`);
                            document.getElementById('statusText').textContent = 'Server response error';
                        }
                    })
                    .catch(error => {
                        log(`❌ Network error: ${error.message}`);
                        document.getElementById('statusText').textContent = 'Network error';
                    });
                    
                } catch (e) {
                    log(`❌ Token decode error: ${e.message}`);
                }
            } else {
                log('❌ No credential in Google response');
            }
        }
        
        function testLogin(role) {
            log(`Testing ${role} login...`);
            
            const testData = {
                email: `test-${role}@example.com`,
                name: `Test ${role.charAt(0).toUpperCase() + role.slice(1)}`,
                picture: 'https://via.placeholder.com/150',
                role: role,
                google_id: `test_${role}_${Date.now()}`
            };
            
            fetch('/google_login.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(testData)
            })
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    log(`✅ Test login response: ${JSON.stringify(data)}`);
                } catch (e) {
                    log(`❌ Test login error: ${text}`);
                }
            })
            .catch(error => {
                log(`❌ Test login network error: ${error.message}`);
            });
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // Initial log
        log('Login flow test page loaded');
    </script>
</body>
</html> 