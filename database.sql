-- Create the database
CREATE DATABASE IF NOT EXISTS review_marketplace CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE review_marketplace;

-- Admin settings table for global configuration
CREATE TABLE IF NOT EXISTS admin_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(50) NOT NULL UNIQUE,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default settings
INSERT INTO admin_settings (setting_key, setting_value, description) VALUES 
('currency', 'INR', 'Platform currency (USD, EUR, INR, etc.)'),
('currency_symbol', '₹', 'Currency symbol to display'),
('signup_bonus_customer', '10.00', 'Bonus amount given to new customers'),
('signup_bonus_reviewer', '0.00', 'Bonus amount given to new reviewers'),
('minimum_payout', '100.00', 'Minimum amount for reviewer withdrawal'),
('platform_fee_percentage', '10.00', 'Platform fee percentage on transactions')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(150) NOT NULL UNIQUE,
    password VARCHAR(255) NULL,
    google_id VARCHAR(100) NULL,
    role ENUM('customer', 'reviewer', 'admin') NOT NULL DEFAULT 'customer',
    wallet_balance DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    status ENUM('active', 'banned') NOT NULL DEFAULT 'active',
    profile_completed BOOLEAN NOT NULL DEFAULT 0,
    profile_picture VARCHAR(255) DEFAULT NULL,
    last_login TIMESTAMP DEFAULT NULL,
    upi_id VARCHAR(100) DEFAULT NULL,
    upi_qr VARCHAR(255) DEFAULT NULL,
    whatsapp VARCHAR(20) DEFAULT NULL,
    signup_bonus_given BOOLEAN NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Reviews table
CREATE TABLE IF NOT EXISTS reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    reviewer_id INT DEFAULT NULL,
    platform VARCHAR(50) NOT NULL,
    business_name VARCHAR(100) NOT NULL,
    business_link VARCHAR(255) DEFAULT NULL,
    country VARCHAR(50) DEFAULT NULL,
    review_text TEXT NOT NULL,
    status ENUM('open', 'claimed', 'submitted', 'completed', 'approved', 'rejected') NOT NULL DEFAULT 'open',
    screenshot_url VARCHAR(255) DEFAULT NULL,
    amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    claimed_at TIMESTAMP DEFAULT NULL,
    FOREIGN KEY (customer_id) REFERENCES users(id),
    FOREIGN KEY (reviewer_id) REFERENCES users(id)
);

-- Wallet transactions table
CREATE TABLE IF NOT EXISTS wallet_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('credit', 'debit', 'topup', 'signup_bonus', 'review_payment', 'withdrawal') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    review_id INT DEFAULT NULL,
    reference VARCHAR(255),
    description TEXT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (review_id) REFERENCES reviews(id)
);

-- Withdrawals table
CREATE TABLE IF NOT EXISTS withdrawals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    upi_id VARCHAR(100) NOT NULL,
    whatsapp VARCHAR(20) NOT NULL,
    status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
    rejection_reason TEXT DEFAULT NULL,
    admin_notes TEXT DEFAULT NULL,
    processed_by INT DEFAULT NULL,
    processed_at TIMESTAMP DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (processed_by) REFERENCES users(id)
);

-- Services table for platform configuration
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    service_name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    base_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    description TEXT,
    icon_class VARCHAR(50) DEFAULT NULL,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default services with rupee pricing
INSERT INTO services (service_name, display_name, base_price, description, icon_class) VALUES 
('google_reviews', 'Google Reviews', 25.00, 'Post reviews on Google My Business', 'fab fa-google'),
('trustpilot', 'Trustpilot Reviews', 30.00, 'Post reviews on Trustpilot platform', 'fas fa-thumbs-up'),
('yelp', 'Yelp Reviews', 35.00, 'Post reviews on Yelp platform', 'fab fa-yelp'),
('amazon', 'Amazon Reviews', 40.00, 'Post product reviews on Amazon', 'fab fa-amazon'),
('facebook', 'Facebook Reviews', 20.00, 'Post reviews on Facebook business pages', 'fab fa-facebook')
ON DUPLICATE KEY UPDATE base_price = VALUES(base_price);

-- AI-powered features table
CREATE TABLE IF NOT EXISTS ai_features (
    id INT AUTO_INCREMENT PRIMARY KEY,
    review_id INT NOT NULL,
    feature_type ENUM('sentiment_analysis', 'screenshot_verification', 'duplicate_review_detection', 'suspicious_pattern_recognition') NOT NULL,
    result TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (review_id) REFERENCES reviews(id)
);

-- Real-time business intelligence
CREATE TABLE IF NOT EXISTS business_intelligence (
    id INT AUTO_INCREMENT PRIMARY KEY,
    business_name VARCHAR(100) NOT NULL,
    revenue_analytics DECIMAL(10,2) NOT NULL,
    trends TEXT NOT NULL,
    user_behavior_tracking TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Multiple payment gateways
CREATE TABLE IF NOT EXISTS payment_gateways (
    id INT AUTO_INCREMENT PRIMARY KEY,
    gateway_name VARCHAR(100) NOT NULL,
    integration_details TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Community engagement
CREATE TABLE IF NOT EXISTS community_engagement (
    id INT AUTO_INCREMENT PRIMARY KEY,
    reviewer_id INT NOT NULL,
    leaderboard_position INT NOT NULL,
    achievement_badges TEXT NOT NULL,
    rewards TEXT NOT NULL,
    social_sharing TEXT NOT NULL,
    referral_program TEXT NOT NULL,
    community_forums TEXT NOT NULL,
    FOREIGN KEY (reviewer_id) REFERENCES users(id)
);

-- Intelligent alert system
- Email notifications with templates
- SMS notifications for urgent updates
- Browser push notifications
- WhatsApp Business integration
- Custom notification preferences

-- Enhanced security measures
- Two-factor authentication (2FA)
- IP-based fraud detection
- Device fingerprinting
- Advanced CAPTCHA systems
- Rate limiting and DDoS protection

-- Build user confidence
- ID verification for reviewers
- Business verification badges
- Review authenticity scores
- Dispute resolution system
- Public review history

-- Speed and scalability
- CDN integration for global delivery
- Database optimization and caching
- Image optimization and lazy loading
- API rate limiting
- Load balancing for high traffic

-- Comprehensive analytics
- Custom report builder
- Export functionality (PDF, Excel)
- Automated report scheduling
- KPI dashboards
- Predictive analytics 

-- Growth and marketing features
- Email marketing integration
- Social media sharing
- SEO optimization tools
- Affiliate program management
- Customer feedback surveys 

-- Expand review platforms
- Amazon reviews
- App Store reviews
- Trustpilot integration
- Sitejabber reviews
- BBB (Better Business Bureau) 

-- Global marketplace
- Multi-language support
- Currency conversion
- Local payment methods
- Regional compliance (GDPR, CCPA)
- Local business directories 

-- Artificial Intelligence integration
- Automated review quality scoring
- Smart review matching algorithm
- Fraud detection using machine learning
- Predictive pricing suggestions
- Automated customer support chatbot 

-- Decentralized features
- Smart contracts for payments
- Immutable review history
- Decentralized identity verification
- Token-based reward system
- Transparent transaction ledger 

-- Streamlined operations
- Automated review approval workflow
- Smart scheduling for review posting
- Automated payment processing
- Intelligent fraud detection
- Self-healing system monitoring 

-- Professional appearance
- Custom logo and branding
- Professional color scheme
- Consistent design language
- Brand guidelines documentation
- Marketing materials 

-- Add indexes for performance
ALTER TABLE users ADD INDEX idx_email (email);
ALTER TABLE users ADD INDEX idx_google_id (google_id);
ALTER TABLE users ADD INDEX idx_role (role);
ALTER TABLE reviews ADD INDEX idx_customer_id (customer_id);
ALTER TABLE reviews ADD INDEX idx_reviewer_id (reviewer_id);
ALTER TABLE reviews ADD INDEX idx_status (status);
ALTER TABLE wallet_transactions ADD INDEX idx_user_id (user_id);
ALTER TABLE wallet_transactions ADD INDEX idx_type (type);
ALTER TABLE withdrawals ADD INDEX idx_user_id (user_id);
ALTER TABLE withdrawals ADD INDEX idx_status (status);
ALTER TABLE admin_settings ADD INDEX idx_setting_key (setting_key); 