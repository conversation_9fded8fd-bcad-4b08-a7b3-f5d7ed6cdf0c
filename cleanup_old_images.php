<?php
/**
 * Automatic Cleanup Script for Review Images
 * 
 * This script should be run via cron job every hour:
 * 0 * * * * /usr/bin/php /path/to/your/project/cleanup_old_images.php
 * 
 * Or run manually for testing
 */

require_once 'includes/db.php';

// Configuration
$cleanup_after_hours = 24; // Delete images older than 24 hours
$log_file = __DIR__ . '/logs/cleanup.log';

// Create logs directory if it doesn't exist
if (!is_dir(__DIR__ . '/logs')) {
    mkdir(__DIR__ . '/logs', 0777, true);
}

function logMessage($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    echo $log_entry;
}

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

try {
    logMessage("Starting automatic cleanup process...");
    
    // Get old review images
    $stmt = $pdo->prepare("
        SELECT id, screenshot_url 
        FROM reviews 
        WHERE status IN ('approved', 'rejected') 
        AND created_at < DATE_SUB(NOW(), INTERVAL ? HOUR) 
        AND screenshot_url IS NOT NULL
    ");
    $stmt->execute([$cleanup_after_hours]);
    $old_reviews = $stmt->fetchAll();
    
    $deleted_count = 0;
    $deleted_size = 0;
    $errors = 0;
    
    logMessage("Found " . count($old_reviews) . " old review images to clean up");
    
    foreach ($old_reviews as $review) {
        $filepath = __DIR__ . '/' . ltrim($review['screenshot_url'], '/');
        
        if (file_exists($filepath)) {
            $file_size = filesize($filepath);
            
            if (unlink($filepath)) {
                $deleted_count++;
                $deleted_size += $file_size;
                logMessage("Deleted: " . basename($filepath) . " (" . formatBytes($file_size) . ")");
            } else {
                $errors++;
                logMessage("ERROR: Failed to delete " . basename($filepath));
            }
        } else {
            logMessage("WARNING: File not found: " . basename($filepath));
        }
    }
    
    // Update database to remove screenshot URLs
    if ($deleted_count > 0) {
        $stmt = $pdo->prepare("
            UPDATE reviews 
            SET screenshot_url = NULL 
            WHERE status IN ('approved', 'rejected') 
            AND created_at < DATE_SUB(NOW(), INTERVAL ? HOUR)
        ");
        $stmt->execute([$cleanup_after_hours]);
        
        logMessage("Database updated: Removed screenshot URLs for cleaned images");
    }
    
    // Summary
    logMessage("Cleanup completed:");
    logMessage("- Files deleted: $deleted_count");
    logMessage("- Storage freed: " . formatBytes($deleted_size));
    logMessage("- Errors: $errors");
    
    // Also clean up any orphaned files (files that exist but aren't in database)
    logMessage("Checking for orphaned files...");
    
    $upload_dir = __DIR__ . '/uploads/reviews/';
    if (is_dir($upload_dir)) {
        $files = glob($upload_dir . '*.webp');
        $orphaned_count = 0;
        $orphaned_size = 0;
        
        foreach ($files as $file) {
            $filename = basename($file);
            $relative_path = '/uploads/reviews/' . $filename;
            
            // Check if this file is referenced in database
            $stmt = $pdo->prepare('SELECT COUNT(*) as count FROM reviews WHERE screenshot_url = ?');
            $stmt->execute([$relative_path]);
            $exists = $stmt->fetch()['count'] > 0;
            
            if (!$exists) {
                $file_size = filesize($file);
                if (unlink($file)) {
                    $orphaned_count++;
                    $orphaned_size += $file_size;
                    logMessage("Deleted orphaned file: $filename (" . formatBytes($file_size) . ")");
                }
            }
        }
        
        if ($orphaned_count > 0) {
            logMessage("Orphaned files cleanup:");
            logMessage("- Orphaned files deleted: $orphaned_count");
            logMessage("- Additional storage freed: " . formatBytes($orphaned_size));
        }
    }
    
    logMessage("Automatic cleanup process completed successfully");
    
} catch (Exception $e) {
    logMessage("ERROR: " . $e->getMessage());
    exit(1);
}

// Optional: Send email notification if significant cleanup occurred
if ($deleted_count > 10 || $deleted_size > 10 * 1024 * 1024) { // More than 10 files or 10MB
    $subject = "1xreviews: Storage Cleanup Report";
    $message = "Automatic cleanup completed:\n";
    $message .= "- Files deleted: $deleted_count\n";
    $message .= "- Storage freed: " . formatBytes($deleted_size) . "\n";
    $message .= "- Date: " . date('Y-m-d H:i:s') . "\n";
    
    // Uncomment to enable email notifications
    // mail('<EMAIL>', $subject, $message);
    
    logMessage("Email notification sent for significant cleanup");
}

logMessage("=== Cleanup process finished ===\n");
?> 