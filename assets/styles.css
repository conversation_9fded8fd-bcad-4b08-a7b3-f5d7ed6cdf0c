/* assets/styles.css */
/* Modern Black Color Theme */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
:root {
    --primary-bg: #fff;
    --primary: #000000;
    --primary-dark: #1a1a1a;
    --secondary: #f8f9fa;
    --accent: #6366f1;
    --text-main: #1f2937;
    --text-light: #6b7280;
    --text-muted: #9ca3af;
    --bg-light: #f8f9fa;
    --bg-white: #ffffff;
    --bg-gray-50: #f9fafb;
    --border-light: #e5e7eb;
    --border-gray: #d1d5db;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --radius: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --glass-bg: rgba(255,255,255,0.98);
    --glass-blur: blur(12px);
    --primary-black: #000000;
    --primary-yellow: #FFD700;
    --secondary-yellow: #FFA500;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    /* 1xreviews Brand Identity System */
    --brand-primary: #1877f2;      /* Facebook Blue */
    --brand-secondary: #42a5f5;    /* Light Blue */
    --brand-accent: #0d47a1;       /* Dark Blue */
    --brand-success: #00c851;      /* Green */
    --brand-warning: #ff9800;      /* Orange */
    --brand-danger: #f44336;       /* Red */
    --brand-info: #2196f3;         /* Info Blue */
    --brand-white: #ffffff;
    --brand-light-gray: #f8f9fa;
    --brand-dark-gray: #343a40;
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-secondary: 'Poppins', sans-serif;
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    --radius-sm: 4px;
    --radius-md: 8px;
    --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.08);
    --shadow-lg: 0 10px 25px rgba(0,0,0,0.15), 0 4px 10px rgba(0,0,0,0.1);
    --shadow-xl: 0 20px 40px rgba(0,0,0,0.15), 0 8px 20px rgba(0,0,0,0.1);
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    /* Neutral Colors */
    --brand-gray-50: #f8f9fa;
    --brand-gray-100: #f1f3f4;
    --brand-gray-200: #e8eaed;
    --brand-gray-300: #dadce0;
    --brand-gray-400: #bdc1c6;
    --brand-gray-500: #9aa0a6;
    --brand-gray-600: #80868b;
    --brand-gray-700: #5f6368;
    --brand-gray-800: #3c4043;
    --brand-gray-900: #202124;
    /* Light Colors */
    --brand-light-blue: #e3f2fd;
    --brand-light-green: #e8f5e8;
    --brand-light-orange: #fff3e0;
    --brand-light-red: #ffebee;
    /* Border Radius */
    --radius-full: 50px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-white) !important;
    color: var(--text-main);
    min-height: 100vh;
    line-height: 1.6;
}

/* Navigation Styles */
.navbar {
    backdrop-filter: blur(10px);
    background: rgba(0, 0, 0, 0.95) !important;
    transition: all 0.3s ease;
}

.navbar-brand {
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.brand-icon {
    display: none;
}

.brand-text {
    background: linear-gradient(135deg, var(--primary-yellow) 0%, var(--secondary-yellow) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}

.navbar-toggler {
    border: none !important;
    padding: 0.5rem;
    transition: all 0.3s ease;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.25rem rgba(255, 215, 0, 0.25) !important;
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-yellow) !important;
    background: rgba(255, 215, 0, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    color: var(--primary-yellow) !important;
    background: rgba(255, 215, 0, 0.15);
}

.navbar-nav .nav-link i {
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover i {
    transform: scale(1.1);
}

/* Dropdown Styles */
.dropdown-menu {
    background: rgba(0, 0, 0, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    margin-top: 0.5rem;
}

.dropdown-item {
    color: rgba(255, 255, 255, 0.8) !important;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0.25rem;
}

.dropdown-item:hover {
    color: var(--primary-yellow) !important;
    background: rgba(255, 215, 0, 0.1) !important;
    transform: translateX(5px);
}

.dropdown-divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 0.5rem 0;
}

/* Navbar Shrink Effect */
.navbar-shrink {
    padding: 0.5rem 0 !important;
    background: rgba(0, 0, 0, 0.98) !important;
}

.navbar-shrink .navbar-brand {
    font-size: 1.3rem;
}

.navbar-shrink .brand-icon {
    width: 35px;
    height: 35px;
}

/* Utility Classes */
.py-5 { padding-top: 3rem; padding-bottom: 3rem; }
.py-md-6 { padding-top: 4rem; padding-bottom: 4rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }
.text-gray-900 { color: var(--gray-900) !important; }
.text-gray-600 { color: var(--gray-600) !important; }
.text-gray-400 { color: var(--gray-400) !important; }
.text-white-50 { color: rgba(255, 255, 255, 0.5) !important; }
.bg-gray-50 { background-color: var(--gray-50) !important; }
.bg-gray-900 { background-color: var(--gray-900) !important; }
.border-gray-700 { border-color: var(--gray-700) !important; }
.hover\:text-white:hover { color: white !important; }

/* Hero Section */
.hero-section {
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    color: #fff;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    pointer-events: none;
}

.hero-bg-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
    pointer-events: none;
    background: #000;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    z-index: 1;
    pointer-events: none;
}

.hero-section > .container {
    position: relative;
    z-index: 2;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    color: white;
}

.hero-description {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

.hero-stats {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
}

.hero-stats .stat-item {
    text-align: center;
}

.hero-stats .stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
}

.hero-stats .stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--brand-gray-900);
    margin-bottom: 0.25rem;
}

.hero-stats .stat-label {
    color: var(--brand-gray-600);
    font-weight: 600;
}

.hero-actions .btn {
    padding: 1rem 2rem;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.hero-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.trust-badges {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.trust-badge {
    display: flex;
    align-items: center;
    background: rgba(255,255,255,0.9);
    color: var(--brand-gray-700);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

/* Modern Button Styles */
.btn-modern {
    background: linear-gradient(135deg, var(--primary-yellow) 0%, var(--secondary-yellow) 100%);
    border: 2px solid var(--primary-yellow);
    color: var(--primary-black);
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
    color: var(--primary-black);
    background: linear-gradient(135deg, var(--secondary-yellow) 0%, var(--primary-yellow) 100%);
}

.btn-modern-outline {
    background: transparent;
    border: 2px solid var(--primary-yellow);
    color: var(--primary-yellow);
    font-weight: 600;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-modern-outline:hover {
    background: var(--primary-yellow);
    color: var(--primary-black);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

/* Stats Section */
.stat-item {
    padding: 1rem;
}

.stat-number {
    font-size: clamp(1.5rem, 4vw, 3rem);
    font-weight: 800;
    color: var(--primary-yellow);
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: clamp(0.875rem, 2vw, 1rem);
    color: rgba(255, 255, 255, 0.7);
}

/* Icon Styles */
.stat-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1rem;
}

.stat-icon i {
    transition: transform 0.3s ease;
}

.stat-item:hover .stat-icon i {
    transform: scale(1.1);
}

/* Service Cards */
.service-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid var(--gray-200);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.service-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 60px;
    height: 60px;
    margin: 0 auto;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--brand-light-gray) 0%, #ffffff 100%);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.service-icon i {
    font-size: 1.5rem;
    transition: transform 0.3s ease;
}

.service-card:hover .service-icon i {
    transform: scale(1.1);
}

/* Modern Step Cards */
.step-card-modern {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.step-card-modern:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.step-number-modern {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;
}

.step-circle {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--gray-800) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.step-text {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--primary-yellow);
}

.step-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100px;
    height: 100px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.step-card-modern:hover .step-icon {
    transform: scale(1.05);
    box-shadow: 0 12px 35px rgba(0,0,0,0.2);
}

.step-icon i {
    color: white;
    font-size: 2.5rem;
    transition: transform 0.3s ease;
}

.step-card-modern:hover .step-icon i {
    transform: scale(1.1);
}

/* Benefit Cards */
.benefit-card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0,0,0,0.05);
    transition: var(--transition-normal);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.benefit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--brand-secondary) 0%, var(--brand-accent) 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.benefit-card:hover::before {
    transform: scaleX(1);
}

.benefit-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.benefit-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--brand-light-gray) 0%, #ffffff 100%);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    transition: var(--transition-normal);
}

.benefit-card:hover .benefit-icon {
    transform: scale(1.1);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.benefit-icon i {
    font-size: 2rem;
    transition: transform 0.3s ease;
}

.benefit-card:hover .benefit-icon i {
    transform: scale(1.1);
}

/* Job Cards */
.job-card-new {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.job-card-new:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.job-content-new {
    position: relative;
    z-index: 2;
}

.job-header {
    border-bottom: 1px solid var(--gray-200);
    padding-bottom: 1rem;
}

.platform-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: var(--brand-light-gray);
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--brand-gray);
}

.platform-badge i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.job-meta {
    border-top: 1px solid var(--gray-200);
    padding-top: 1rem;
}

.job-meta i {
    color: var(--brand-gray);
    margin-right: 0.25rem;
}

.job-overlay-new {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.8) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 3;
}

.job-card-new:hover .job-overlay-new {
    opacity: 1;
}

.overlay-content-new {
    text-align: center;
    color: white;
    padding: 2rem;
}

/* CTA Section */
.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-stats {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2rem;
    backdrop-filter: blur(10px);
}

.cta-stat {
    text-align: center;
}

.cta-stat-number {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    font-weight: 800;
    color: var(--primary-yellow);
    margin-bottom: 0.5rem;
}

.cta-stat-label {
    font-size: clamp(0.875rem, 2vw, 1rem);
    color: rgba(255, 255, 255, 0.8);
}

/* FAQ Section */
.accordion-button {
    background: white !important;
    border: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    border-radius: 8px !important;
    font-weight: 600;
    color: var(--gray-900) !important;
    padding: 1.5rem !important;
}

.accordion-button:not(.collapsed) {
    background: var(--primary-yellow) !important;
    color: var(--primary-black) !important;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3) !important;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(255, 215, 0, 0.25) !important;
}

.accordion-body {
    padding: 1.5rem !important;
    border-radius: 0 0 8px 8px !important;
    border-top: 1px solid var(--gray-200);
}

/* Footer */
footer {
    background: var(--primary) !important;
    color: #fff !important;
    border-top: 1px solid var(--border-light);
}

footer * {
    color: #fff !important;
}

footer a:hover {
    color: var(--primary-yellow) !important;
}

/* Footer Icon Styles */
.footer-link {
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.footer-link:hover {
    transform: translateX(5px);
}

.footer-link i {
    transition: transform 0.3s ease;
}

.footer-link:hover i {
    transform: scale(1.2);
}

/* Social Media Icons */
.social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255,255,255,0.1);
    transition: all 0.3s ease;
}

.social-icon:hover {
    background: var(--brand-secondary);
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        gap: 1rem;
    }
    
    .btn-modern, .btn-modern-outline {
        width: 100%;
        max-width: 300px;
    }
    
    .cta-stats {
        padding: 1rem;
    }
    
    .cta-stat-number {
        font-size: 2rem;
    }
    
    .step-connector {
        display: none !important;
    }
    
    .service-card, .step-card-modern, .benefit-card, .job-card-new {
        margin-bottom: 1rem;
    }
    
    .navbar-nav .nav-link {
        padding: 1rem !important;
        margin: 0.25rem 0;
    }
    
    .dropdown-menu {
        background: rgba(0, 0, 0, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .service-icon {
        width: 50px;
        height: 50px;
    }
    
    .service-icon i {
        font-size: 1.25rem;
    }
    
    .benefit-icon {
        width: 60px;
        height: 60px;
    }
    
    .benefit-icon i {
        font-size: 1.5rem;
    }
    
    .step-icon {
        width: 80px;
        height: 80px;
    }
    
    .step-icon i {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .display-6 {
        font-size: 2rem;
    }
    
    .py-5 {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
    
    .py-md-6 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
    
    .service-card, .step-card-modern, .benefit-card, .job-card-new {
        margin-bottom: 1rem;
    }
    
    .cta-stats {
        padding: 1rem;
    }
    
    .cta-stat-number {
        font-size: 2rem;
    }
    
    .navbar-brand {
        font-size: 1.3rem;
    }
    
    .brand-icon {
        width: 35px;
        height: 35px;
    }
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Focus States */
.btn:focus, .btn-modern:focus, .btn-modern-outline:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.25);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print Styles */
@media print {
    .hero-section, .btn, .modal {
        display: none !important;
    }
    
    body {
        color: black !important;
        background: white !important;
    }
}

/* Brand Logo Styles */
.brand-logo {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-family: var(--font-primary);
    font-weight: 700;
    text-decoration: none;
    color: var(--brand-primary);
}

.brand-logo .logo-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--brand-white);
    font-size: 1.2rem;
    font-weight: 800;
}

.brand-logo .logo-text {
    font-size: 1.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 50%, var(--brand-accent) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Brand Button Styles */
.btn-brand {
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border: none;
    color: var(--brand-white);
    font-weight: 600;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-brand:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--brand-white);
    text-decoration: none;
}

.btn-brand-outline {
    background: transparent;
    border: 2px solid var(--brand-primary);
    color: var(--brand-primary);
    font-weight: 600;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.btn-brand-outline:hover {
    background: var(--brand-primary);
    color: var(--brand-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    text-decoration: none;
}

/* Brand Card Styles */
.brand-card {
    background: var(--brand-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-xl);
    transition: var(--transition-normal);
    border: 1px solid rgba(0,0,0,0.05);
}

.brand-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* Brand Section Styles */
.brand-section {
    padding: var(--spacing-xxl) 0;
}

.brand-section-title {
    font-family: var(--font-secondary);
    font-weight: 700;
    color: var(--brand-primary);
    margin-bottom: var(--spacing-lg);
}

.brand-section-subtitle {
    color: var(--brand-gray);
    font-size: 1.1rem;
    margin-bottom: var(--spacing-xl);
}

/* Brand Badge Styles */
.brand-badge {
    background: linear-gradient(135deg, var(--brand-secondary) 0%, var(--brand-accent) 100%);
    color: var(--brand-white);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-xl);
    font-size: 0.875rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
}

/* Brand Alert Styles */
.alert-brand {
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    color: var(--brand-white);
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

/* Brand Progress Bar */
.progress-brand {
    background: var(--brand-light-gray);
    border-radius: var(--radius-xl);
    height: 8px;
    overflow: hidden;
}

.progress-brand-bar {
    background: linear-gradient(135deg, var(--brand-secondary) 0%, var(--brand-accent) 100%);
    height: 100%;
    border-radius: var(--radius-xl);
    transition: var(--transition-normal);
}

/* Brand Loading Spinner */
.spinner-brand {
    width: 40px;
    height: 40px;
    border: 4px solid var(--brand-light-gray);
    border-top: 4px solid var(--brand-secondary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Brand Typography */
.text-brand-primary { color: var(--brand-primary) !important; }
.text-brand-secondary { color: var(--brand-secondary) !important; }
.text-brand-accent { color: var(--brand-accent) !important; }

.bg-brand-primary { background-color: var(--brand-primary) !important; }
.bg-brand-secondary { background-color: var(--brand-secondary) !important; }
.bg-brand-accent { background-color: var(--brand-accent) !important; }

/* Responsive Brand Adjustments */
@media (max-width: 768px) {
    .brand-logo .logo-text {
        font-size: 1.25rem;
    }
    
    .brand-logo .logo-icon {
        width: 28px;
        height: 28px;
        font-size: 1rem;
    }
    
    .brand-section {
        padding: var(--spacing-xl) 0;
    }
}

/* Ensure brand logo works in navbar */
.navbar-brand.brand-logo {
    color: var(--brand-white) !important;
}

.navbar-brand.brand-logo .logo-text {
    color: var(--brand-white);
    -webkit-text-fill-color: var(--brand-white);
    background: none;
}

.navbar-brand.brand-logo .logo-icon {
    background: linear-gradient(135deg, var(--brand-secondary) 0%, var(--brand-accent) 100%);
}

/* Social Media Services Section */
.social-service-card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0,0,0,0.05);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.social-service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.coming-soon-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: linear-gradient(135deg, var(--brand-secondary) 0%, var(--brand-accent) 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-xl);
    font-size: 0.875rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    z-index: 10;
}

.coming-soon-badge i {
    font-size: 0.75rem;
}

.social-service-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100px;
    height: 100px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--brand-light-gray) 0%, #ffffff 100%);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    transition: var(--transition-normal);
}

.social-service-card:hover .social-service-icon {
    transform: scale(1.1);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.service-features {
    text-align: left;
    margin-top: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    color: var(--brand-gray);
}

.feature-item i {
    margin-right: 0.5rem;
    font-size: 0.875rem;
}

/* Completely Redesigned How It Works Section */
.bg-gradient-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Timeline Design */
.timeline-container {
    position: relative;
    padding-left: 2rem;
}

.timeline-step {
    position: relative;
    margin-bottom: 3rem;
}

.timeline-step:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.marker-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    position: relative;
    z-index: 10;
    transition: var(--transition-normal);
}

.timeline-step:hover .marker-icon {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(0,0,0,0.2);
}

.marker-line {
    width: 3px;
    height: 80px;
    background: linear-gradient(180deg, var(--brand-secondary) 0%, rgba(255,215,0,0.3) 100%);
    margin-top: 0.5rem;
    border-radius: 2px;
}

.timeline-step:last-child .marker-line {
    display: none;
}

.timeline-content {
    margin-left: 1rem;
}

.step-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(0,0,0,0.05);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.step-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--brand-secondary) 0%, var(--brand-accent) 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.step-card:hover::before {
    transform: scaleX(1);
}

.step-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.step-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--brand-gray-900);
    margin: 0;
}

.step-time {
    background: linear-gradient(135deg, var(--brand-secondary) 0%, var(--brand-accent) 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 600;
}

.step-description {
    color: var(--brand-gray-600);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.step-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.feature-tag {
    background: var(--brand-light-gray);
    color: var(--brand-gray-700);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.feature-tag i {
    color: var(--brand-success);
    font-size: 0.75rem;
}

/* Stats Showcase */
.stats-showcase {
    background: white;
    border-radius: var(--radius-xl);
    padding: 3rem 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(0,0,0,0.05);
}

.stat-item {
    padding: 1.5rem;
    transition: var(--transition-normal);
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--brand-light-gray) 0%, #ffffff 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: var(--brand-primary);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.stat-item:hover .stat-icon {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--brand-gray-900);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-label {
    color: var(--brand-gray-600);
    font-weight: 600;
    font-size: 1rem;
}

/* Hero CTA */
.cta-hero {
    background: white;
    border-radius: var(--radius-xl);
    padding: 3rem 2rem;
    box-shadow: var(--shadow-lg);
    border: 2px solid var(--brand-light-gray);
    transition: var(--transition-normal);
}

.cta-hero:hover {
    border-color: var(--brand-secondary);
    transform: translateY(-2px);
}

.btn-hero-primary {
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    color: white;
    border: none;
    padding: 1.25rem 2rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.btn-hero-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-hero-primary:hover::before {
    left: 100%;
}

.btn-hero-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.btn-hero-secondary {
    background: white;
    color: var(--brand-gray-900);
    border: 2px solid var(--brand-secondary);
    padding: 1.25rem 2rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 1.1rem;
    transition: var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.btn-hero-secondary:hover {
    background: var(--brand-secondary);
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.btn-hero-primary small,
.btn-hero-secondary small {
    font-size: 0.875rem;
    opacity: 0.8;
    font-weight: 400;
}

/* Responsive Timeline */
@media (max-width: 768px) {
    .timeline-container {
        padding-left: 1rem;
    }
    
    .timeline-marker {
        left: -1rem;
    }
    
    .marker-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .marker-line {
        height: 60px;
    }
    
    .step-card {
        padding: 1.5rem;
    }
    
    .step-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .stats-showcase {
        padding: 2rem 1rem;
    }
    
    .stat-item {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
    
    .cta-hero {
        padding: 2rem 1rem;
    }
    
    .btn-hero-primary,
    .btn-hero-secondary {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }
}

/* Platform Icon Styles */
.platform-icon {
    width: 32px;
    height: 32px;
    object-fit: contain;
    border-radius: 4px;
}

.social-service-icon .platform-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
}

.service-icon .platform-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
}

/* Fallback icon styles */
.service-icon i,
.social-service-icon i {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Ensure icons are properly sized */
.service-icon-wrapper .service-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
}

.social-service-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 100px;
}

/* Icon fallback handling */
.service-icon img:not([src*="data:"]),
.social-service-icon img:not([src*="data:"]) {
    max-width: 100%;
    max-height: 100%;
}

/* Ensure proper icon display */
.fab, .fas {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Refined Why Choose Section */
.image-container {
    position: relative;
    overflow: hidden;
}

.floating-stats {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.stat-bubble {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    padding: 1rem;
    text-align: center;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: float 3s ease-in-out infinite;
}

.stat-bubble:nth-child(2) {
    animation-delay: 1.5s;
}

.stat-bubble .stat-number {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--brand-primary);
    margin-bottom: 0.25rem;
}

.stat-bubble .stat-label {
    font-size: 0.875rem;
    color: var(--brand-gray);
    font-weight: 600;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.trust-indicators {
    border: 1px solid rgba(0,0,0,0.05);
    background: linear-gradient(135deg, var(--brand-light-gray) 0%, #ffffff 100%);
}

.trust-item {
    display: flex;
    align-items: center;
    font-size: 0.9rem;
    color: var(--brand-gray);
    font-weight: 500;
}

.trust-item i {
    font-size: 1rem;
}

/* Enhanced Benefit Cards */
.benefit-card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(0,0,0,0.05);
    transition: var(--transition-normal);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.benefit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--brand-secondary) 0%, var(--brand-accent) 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.benefit-card:hover::before {
    transform: scaleX(1);
}

.benefit-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.benefit-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--brand-light-gray) 0%, #ffffff 100%);
    box-shadow: 0 6px 20px rgba(0,0,0,0.1);
    transition: var(--transition-normal);
}

.benefit-card:hover .benefit-icon {
    transform: scale(1.1);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.benefit-icon i {
    font-size: 2rem;
    transition: transform 0.3s ease;
}

.benefit-card:hover .benefit-icon i {
    transform: scale(1.1);
}

/* Compact Recent Review Opportunities Section */
.opportunity-card-compact {
    background: white;
    border-radius: 12px;
    padding: 1.25rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid rgba(0,0,0,0.04);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.opportunity-card-compact:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.opportunity-main {
    position: relative;
    z-index: 1;
}

.opportunity-header-compact {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.business-info-compact {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.business-avatar-compact {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.business-name-compact {
    font-weight: 600;
    color: var(--brand-gray-900);
    margin: 0 0 0.25rem 0;
    font-size: 0.95rem;
    line-height: 1.3;
}

.platform-badge {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    background: var(--brand-light-gray);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--brand-gray-700);
}

.platform-badge i {
    font-size: 0.7rem;
}

.reward-badge {
    background: linear-gradient(135deg, var(--brand-success) 0%, #28a745 100%);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    font-weight: 700;
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    flex-shrink: 0;
}

.opportunity-meta-compact {
    display: flex;
    gap: 1rem;
    font-size: 0.8rem;
    color: var(--brand-gray-600);
}

.time-badge, .claimed-badge {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.time-badge i, .claimed-badge i {
    font-size: 0.7rem;
    opacity: 0.7;
}

/* Compact Lock Overlay */
.lock-overlay-compact {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0,0,0,0.85) 0%, rgba(0,0,0,0.95) 100%);
    backdrop-filter: blur(3px);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 10;
}

.opportunity-card-compact:hover .lock-overlay-compact {
    opacity: 1;
    visibility: visible;
}

.lock-content-compact {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: white;
    text-align: center;
}

.lock-content-compact i {
    font-size: 1.5rem;
    opacity: 0.9;
}

.lock-content-compact span {
    font-size: 0.85rem;
    font-weight: 500;
    opacity: 0.9;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .opportunity-card-compact {
        padding: 1rem;
    }
    
    .business-avatar-compact {
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }
    
    .business-name-compact {
        font-size: 0.9rem;
    }
    
    .platform-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
    
    .reward-badge {
        padding: 0.4rem 0.6rem;
        font-size: 0.85rem;
    }
    
    .opportunity-meta-compact {
        font-size: 0.75rem;
        gap: 0.75rem;
    }
    
    .lock-overlay-compact {
        opacity: 1;
        visibility: visible;
        background: linear-gradient(135deg, rgba(0,0,0,0.75) 0%, rgba(0,0,0,0.85) 100%);
    }
    
    .lock-content-compact i {
        font-size: 1.25rem;
    }
    
    .lock-content-compact span {
        font-size: 0.8rem;
    }
}

/* Facebook-inspired Navbar */
.navbar.bg-primary {
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-accent) 100%) !important;
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    font-size: 1.5rem;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.logo-text {
    color: white;
    font-weight: 700;
}

.navbar-nav .nav-link {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: white !important;
    background: rgba(255,255,255,0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link i {
    font-size: 0.9rem;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--brand-gray-50) 0%, var(--brand-light-blue) 100%);
    position: relative;
    overflow: hidden;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(24, 119, 242, 0.1);
    color: var(--brand-primary);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    color: var(--brand-gray-900);
}

.hero-description {
    font-size: 1.25rem;
    color: var(--brand-gray-600);
    line-height: 1.6;
}

.hero-stats {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
}

.hero-stats .stat-item {
    text-align: center;
}

.hero-stats .stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
}

.hero-stats .stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--brand-gray-900);
    margin-bottom: 0.25rem;
}

.hero-stats .stat-label {
    color: var(--brand-gray-600);
    font-weight: 600;
}

.hero-actions .btn {
    padding: 1rem 2rem;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.hero-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.trust-badges {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.trust-badge {
    display: flex;
    align-items: center;
    background: rgba(255,255,255,0.8);
    color: var(--brand-gray-700);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

/* Hero Visual */
.hero-image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.hero-image {
    width: 100%;
    height: auto;
    border-radius: 20px;
}

.floating-card {
    position: absolute;
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: float 3s ease-in-out infinite;
}

.floating-card.card-1 {
    top: 10%;
    left: -10%;
    animation-delay: 0s;
}

.floating-card.card-2 {
    top: 50%;
    right: -10%;
    animation-delay: 1s;
}

.floating-card.card-3 {
    bottom: 10%;
    left: 10%;
    animation-delay: 2s;
}

.card-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.card-content {
    flex: 1;
}

.card-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--brand-gray-900);
    margin-bottom: 0.25rem;
}

.card-amount {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--brand-success);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Footer */
.footer {
    background: linear-gradient(135deg, var(--brand-gray-900) 0%, #000 100%);
}

.footer-brand .brand-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.footer-brand .logo-icon {
    width: 40px;
    height: 40px;
    background: var(--brand-primary);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.footer-brand .logo-text {
    color: white;
    font-weight: 700;
    font-size: 1.5rem;
}

.footer-heading {
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.footer-heading i {
    color: var(--brand-primary);
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: rgba(255,255,255,0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.footer-links a:hover {
    color: var(--brand-primary);
    transform: translateX(5px);
}

.footer-links a i {
    font-size: 0.7rem;
    color: var(--brand-primary);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--brand-primary);
    color: white;
    transform: translateY(-2px);
}

.footer-divider {
    border-color: rgba(255,255,255,0.1);
}

.footer-bottom-links {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.footer-bottom-link {
    color: rgba(255,255,255,0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-bottom-link:hover {
    color: var(--brand-primary);
}

.footer-copyright {
    color: rgba(255,255,255,0.7);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-description {
        font-size: 1.1rem;
    }
    
    .hero-stats {
        padding: 1.5rem;
    }
    
    .hero-stats .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .hero-stats .stat-number {
        font-size: 1.5rem;
    }
    
    .trust-badges {
        justify-content: center;
    }
    
    .floating-card {
        position: relative;
        margin-bottom: 1rem;
        animation: none;
    }
    
    .footer-bottom-links {
        justify-content: center;
        margin-bottom: 1rem;
    }
    
    .footer-copyright {
        text-align: center;
    }
}

/* Services Section */
.services-section {
    background: linear-gradient(135deg, var(--brand-gray-50) 0%, white 100%);
}

.section-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(24, 119, 242, 0.1);
    color: var(--brand-primary);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--brand-gray-900);
    line-height: 1.2;
}

.section-description {
    font-size: 1.1rem;
    color: var(--brand-gray-600);
    line-height: 1.6;
}

.service-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid var(--brand-gray-200);
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.service-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 16px;
    color: white;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

.service-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--brand-gray-900);
    margin-bottom: 0.75rem;
}

.service-description {
    color: var(--brand-gray-600);
    font-size: 0.95rem;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.service-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.feature-badge {
    display: inline-flex;
    align-items: center;
    background: var(--brand-light-green);
    color: var(--brand-success);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Coming Soon Section */
.coming-soon-section {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: var(--shadow-lg);
    border: 2px dashed var(--brand-gray-300);
}

.coming-soon-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--brand-gray-900);
    text-align: center;
}

.coming-soon-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: 12px;
    background: var(--brand-gray-50);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.coming-soon-card:hover {
    background: var(--brand-light-blue);
    transform: translateY(-2px);
}

.coming-soon-icon {
    width: 60px;
    height: 60px;
    background: var(--brand-gray-300);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--brand-gray-600);
    font-size: 1.5rem;
    margin: 0 auto 1rem;
    position: relative;
}

.lock-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.coming-soon-card h6 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--brand-gray-700);
    margin-bottom: 0.5rem;
}

.coming-soon-description {
    font-size: 0.85rem;
    color: var(--brand-gray-500);
    margin: 0;
}

/* How It Works Section */
.how-it-works-section {
    background: linear-gradient(135deg, white 0%, var(--brand-light-blue) 100%);
}

.step-card {
    background: white;
    border-radius: 16px;
    padding: 2.5rem 2rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid var(--brand-gray-200);
}

.step-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
}

.step-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.step-number {
    position: absolute;
    top: -10px;
    right: 20px;
    width: 40px;
    height: 40px;
    background: var(--brand-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
}

.step-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.step-card:hover .step-icon {
    transform: scale(1.1);
}

.step-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--brand-gray-900);
    margin-bottom: 1rem;
}

.step-description {
    color: var(--brand-gray-600);
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.step-features {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.step-feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--brand-gray-700);
    font-size: 0.9rem;
}

.step-feature i {
    color: var(--brand-success);
    font-size: 1rem;
}

/* Stats Showcase */
.stats-showcase {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: var(--shadow-lg);
    margin-top: 3rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: var(--brand-gray-50);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    background: var(--brand-light-blue);
    transform: translateY(-2px);
}

.stat-card .stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--brand-gray-900);
    margin-bottom: 0.25rem;
}

.stat-label {
    color: var(--brand-gray-600);
    font-weight: 600;
    font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
    background: white;
    border-radius: 20px;
    padding: 3rem;
    box-shadow: var(--shadow-lg);
    margin-top: 3rem;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--brand-gray-900);
}

.cta-description {
    font-size: 1.1rem;
    color: var(--brand-gray-600);
    line-height: 1.6;
}

.cta-buttons .btn {
    padding: 1rem 2rem;
    font-weight: 600;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.cta-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Benefits Section */
.benefits-section {
    background: linear-gradient(135deg, white 0%, var(--brand-gray-50) 100%);
}

.benefits-visual {
    position: relative;
}

.benefits-image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.benefits-image {
    width: 100%;
    height: auto;
    border-radius: 20px;
}

.floating-benefit-stats {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.benefit-stat-bubble {
    position: absolute;
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: float 3s ease-in-out infinite;
}

.benefit-stat-bubble:nth-child(1) {
    top: 10%;
    right: -20px;
    animation-delay: 0s;
}

.benefit-stat-bubble:nth-child(2) {
    bottom: 10%;
    left: -20px;
    animation-delay: 1.5s;
}

.benefit-stat-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.benefit-stat-content {
    flex: 1;
}

.benefit-stat-number {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--brand-gray-900);
    margin-bottom: 0.25rem;
}

.benefit-stat-label {
    font-size: 0.8rem;
    color: var(--brand-gray-600);
    font-weight: 500;
}

.benefit-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid var(--brand-gray-200);
    text-align: center;
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--brand-primary);
}

.benefit-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.75rem;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.benefit-card:hover .benefit-icon {
    transform: scale(1.1);
}

.benefit-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--brand-gray-900);
    margin-bottom: 1rem;
}

.benefit-description {
    color: var(--brand-gray-600);
    font-size: 0.95rem;
    line-height: 1.6;
    margin: 0;
}

.trust-indicators {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--brand-gray-200);
}

.trust-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--brand-gray-900);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.trust-title i {
    color: var(--brand-primary);
}

.trust-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.trust-badges .trust-badge {
    display: flex;
    align-items: center;
    background: var(--brand-light-green);
    color: var(--brand-success);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
}

.trust-badges .trust-badge i {
    font-size: 0.8rem;
}

/* FAQ Section */
.faq-section {
    background: linear-gradient(135deg, var(--brand-gray-50) 0%, white 100%);
}

.faq-container {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
}

.faq-item {
    border: 1px solid var(--brand-gray-200);
    border-radius: 12px;
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    border-color: var(--brand-primary);
    box-shadow: var(--shadow-md);
}

.faq-item:last-child {
    margin-bottom: 0;
}

.faq-header {
    background: white;
}

.faq-button {
    width: 100%;
    padding: 1.5rem;
    background: none;
    border: none;
    display: flex;
    align-items: center;
    gap: 1rem;
    text-align: left;
    cursor: pointer;
    transition: all 0.3s ease;
}

.faq-button:hover {
    background: var(--brand-gray-50);
}

.faq-button:focus {
    outline: none;
    box-shadow: none;
}

.faq-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.faq-content {
    flex: 1;
}

.faq-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--brand-gray-900);
    margin-bottom: 0.5rem;
}

.faq-preview {
    font-size: 0.9rem;
    color: var(--brand-gray-600);
    margin: 0;
}

.faq-arrow {
    width: 30px;
    height: 30px;
    background: var(--brand-gray-100);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--brand-gray-600);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.faq-button[aria-expanded="true"] .faq-arrow {
    transform: rotate(180deg);
    background: var(--brand-primary);
    color: white;
}

.faq-body {
    padding: 0 1.5rem 1.5rem;
    color: var(--brand-gray-600);
    line-height: 1.6;
}

.faq-body p {
    margin: 0;
    font-size: 0.95rem;
}

/* Platform Icon Cards */
.platform-icon-card {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 2px solid transparent;
}

.platform-icon-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--brand-primary);
}

.platform-icon-small {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    transition: all var(--transition-normal);
    position: relative;
}

.platform-icon-small i {
    font-size: 1.5rem;
    color: white;
    transition: all var(--transition-normal);
}

.platform-icon-card:hover .platform-icon-small {
    transform: scale(1.1);
    box-shadow: 0 10px 25px rgba(24, 119, 242, 0.3);
}

.platform-icon-card:hover .platform-icon-small i {
    transform: scale(1.1);
}

/* Coming Soon Icon Cards */
.coming-soon-icon-card {
    background: var(--bg-white);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 2px solid var(--brand-gray-200);
    position: relative;
    overflow: hidden;
}

.coming-soon-icon-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.coming-soon-icon-small {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--brand-gray-300), var(--brand-gray-400));
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    transition: all var(--transition-normal);
    position: relative;
}

.coming-soon-icon-small i {
    font-size: 1.5rem;
    color: var(--brand-gray-600);
    transition: all var(--transition-normal);
}

.lock-overlay-small {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all var(--transition-normal);
}

.lock-overlay-small i {
    color: white;
    font-size: 1rem;
}

.coming-soon-icon-card:hover .lock-overlay-small {
    opacity: 1;
}

.coming-soon-icon-card:hover .coming-soon-icon-small {
    transform: scale(1.05);
}

/* Responsive adjustments for platform icons */
@media (max-width: 768px) {
    .platform-icon-small,
    .coming-soon-icon-small {
        width: 50px;
        height: 50px;
    }
    
    .platform-icon-small i,
    .coming-soon-icon-small i {
        font-size: 1.25rem;
    }
    
    .platform-icon-card,
    .coming-soon-icon-card {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .platform-icon-small,
    .coming-soon-icon-small {
        width: 45px;
        height: 45px;
    }
    
    .platform-icon-small i,
    .coming-soon-icon-small i {
        font-size: 1.1rem;
    }
}

/* Hero Video Background */
.hero-bg-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -2;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    z-index: -1;
}

.hero-section {
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    color: #fff;
}

.hero-title.display-2 {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    color: #fff;
}

.hero-search-form {
    max-width: 700px;
    margin: 0 auto 1.5rem auto;
    background: rgba(255,255,255,0.1);
    border-radius: 50px;
    padding: 0.5rem 1rem;
    box-shadow: 0 2px 12px rgba(0,0,0,0.12);
}

.hero-search-input {
    border: none;
    background: transparent;
    color: #fff;
    font-size: 1.25rem;
    border-radius: 50px;
    outline: none;
}

.hero-search-input::placeholder {
    color: #eee;
    opacity: 1;
}

.hero-search-btn {
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    font-size: 1.25rem;
}

.hero-tags {
    gap: 1rem;
}

.hero-tags .btn {
    border: 1.5px solid #fff;
    color: #fff;
    background: rgba(255,255,255,0.08);
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.2s;
}

.hero-tags .btn:hover {
    background: #fff;
    color: #222;
}

.trusted-by {
    gap: 2rem;
    opacity: 0.85;
    margin-top: 2rem;
}

.trusted-label {
    font-size: 1rem;
    color: #fff;
    opacity: 0.7;
}

.trusted-logo {
    height: 28px;
    opacity: 0.85;
    filter: grayscale(1) brightness(1.2);
    margin-right: 0.5rem;
}

@media (max-width: 768px) {
    .hero-title.display-2 {
        font-size: 2.2rem;
    }
    .hero-search-form {
        flex-direction: column;
        padding: 0.5rem;
    }
    .hero-search-btn {
        width: 100%;
        margin-top: 0.5rem;
    }
    .hero-tags {
        gap: 0.5rem;
    }
    .trusted-by {
        gap: 1rem;
    }
}

/* =================================
   REDESIGNED HOMEPAGE SECTIONS
   ================================= */

/* Minimalistic Reviews Completed Section */
.reviews-completed-section {
    padding: 2rem;
    background: white;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    max-width: 400px;
    margin: 0 auto;
}

.completed-icon {
    font-size: 3rem;
    color: #28a745;
}

.completed-counter {
    font-size: 3.5rem;
    font-weight: 800;
    color: #1f2937;
    margin: 0.5rem 0;
}

.completed-label {
    font-size: 1.1rem;
    color: #6b7280;
    font-weight: 500;
}

.progress-bar-container {
    width: 100%;
    height: 6px;
    background: #e5e7eb;
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 3px;
    width: 0%;
    animation: fillProgress 2s ease-out forwards;
}

@keyframes fillProgress {
    to { width: 75%; }
}

/* Modern Platform Cards - App-like Design */
.platform-card-modern {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
    height: 100%;
    cursor: pointer;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* shadow-md */
}

.platform-card-modern:hover {
    transform: scale(1.02); /* hover:scale-105 */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* shadow-lg */
    border-color: #3b82f6;
}

.platform-logo-modern {
    width: 48px;
    height: 48px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.platform-logo-modern img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.platform-name-modern {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

/* Service Type Selection */
.service-type-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #e5e7eb;
    height: 100%;
}

.service-type-card:hover,
.service-type-card.active {
    border-color: #007bff;
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
}

.service-type-card .service-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
}

.service-type-card h5 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.service-type-card p {
    color: #6b7280;
    margin: 0;
    font-size: 0.9rem;
}

/* Country Flags */
.country-flags {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    align-items: center;
}

.flag-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    border: 2px solid transparent;
    min-width: 100px;
}

.flag-option:hover,
.flag-option.active {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #007bff;
}

.flag-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.flag-option span {
    font-size: 0.9rem;
    font-weight: 500;
    color: #1f2937;
}

/* TikTok Services */
.tiktok-service-card {
    background: white;
    padding: 3rem 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.tiktok-logo-container {
    margin-bottom: 2rem;
}

.tiktok-logo {
    width: 80px;
    height: auto;
}

.service-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.service-description {
    color: #6b7280;
    font-size: 1.1rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.service-features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.service-features-grid .feature-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    font-weight: 500;
    color: #1f2937;
}

.service-features-grid .feature-item i {
    color: #007bff;
    font-size: 1.1rem;
}

.btn-tiktok {
    background: linear-gradient(135deg, #ff0050, #ff4081);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.btn-tiktok:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 0, 80, 0.3);
    color: white;
}

/* How to Start Steps */
.start-step-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    height: 100%;
    position: relative;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.start-step-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.step-number-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.9rem;
}

.step-icon-container {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1rem auto 1.5rem;
    font-size: 1.8rem;
    color: #007bff;
    transition: all 0.3s ease;
}

.start-step-card:hover .step-icon-container {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    transform: scale(1.1);
}

.step-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.step-description {
    color: #6b7280;
    line-height: 1.6;
    margin: 0;
}

/* Counter Animation */
@keyframes countUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.completed-counter {
    animation: countUp 1s ease-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .platform-card {
        padding: 1.5rem 1rem;
    }
    
    .platform-logo {
        width: 48px;
        height: 48px;
        margin-bottom: 1rem;
    }
    
    .service-features-grid {
        grid-template-columns: 1fr;
    }
    
    .country-flags {
        gap: 0.5rem;
    }
    
    .flag-option {
        min-width: 80px;
        padding: 0.75rem;
    }
    
    .flag-icon {
        font-size: 2rem;
    }
    
    .start-step-card {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .step-icon-container {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .completed-counter {
        font-size: 2.5rem;
    }
    
    .reviews-completed-section {
        padding: 1.5rem;
    }
    
    .tiktok-service-card {
        padding: 2rem 1.5rem;
    }
    
    .service-title {
        font-size: 1.5rem;
    }
}

/* Modern Opportunity Cards - App-like Design */
.opportunity-card-modern {
    background: white;
    border-radius: 12px; /* rounded-xl */
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* shadow-md */
}

.opportunity-card-modern:hover {
    transform: scale(1.02); /* hover:scale-105 */
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* shadow-lg */
    border-color: #3b82f6;
    background-color: #f8fafc;
}

.opportunity-header-modern {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1.25rem;
    border-bottom: 1px solid #f3f4f6;
}

.business-info-modern {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
}

.business-name-modern {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.25rem;
}

.platform-badge-modern {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.platform-badge-modern i {
    font-size: 0.875rem;
}

.reward-badge-modern {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 600;
}

.opportunity-meta-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.25rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.time-badge-modern,
.claimed-badge-modern {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.lock-overlay-modern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.opportunity-card-modern:hover .lock-overlay-modern {
    opacity: 1;
}

.lock-content-modern {
    text-align: center;
    color: white;
}

.lock-content-modern i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.lock-content-modern span {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Modern Service Cards - App-like Design */
.service-card-modern {
    background: white;
    padding: 1.5rem; /* p-6 */
    border-radius: 12px; /* rounded-xl */
    border: 1px solid #e5e7eb;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
    cursor: pointer;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); /* shadow-md */
}

.service-card-modern:hover {
    transform: scale(1.05); /* hover:scale-105 */
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-xl */
    border-color: #3b82f6;
    background-color: #f8fafc; /* slight background change */
}

.service-icon-modern {
    width: 48px;
    height: 48px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 8px;
    font-size: 1.5rem;
    color: #007bff;
}

.service-icon-modern img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.service-title-modern {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.service-desc-modern {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.5;
    margin: 0;
}

/* Responsive Updates */
@media (max-width: 768px) {
    .platform-card-modern {
        padding: 1rem;
    }
    
    .platform-logo-modern {
        width: 40px;
        height: 40px;
    }
    
    .opportunity-header-modern {
        padding: 1rem;
    }
    
    .business-name-modern {
        font-size: 0.9rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 0.25rem;
    }
    
    .service-card-modern {
        padding: 1.25rem;
    }
    
    .service-icon-modern {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }
}

/* Modern Navbar Styling */
.navbar-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 0.75rem 0;
    transition: all 0.3s ease;
}

.brand-logo-modern {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: #1f2937;
    font-weight: 700;
    font-size: 1.25rem;
}

.brand-icon-modern {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.brand-text-modern {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-link-modern {
    color: #6b7280 !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.nav-link-modern:hover, .nav-link-modern.active {
    color: #3b82f6 !important;
    background-color: #f8fafc;
}

.nav-link-modern.btn-login {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white !important;
    border-radius: 8px;
    padding: 0.5rem 1.25rem !important;
}

.nav-link-modern.btn-login:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    color: white !important;
}

.dropdown-menu-modern {
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.dropdown-item-modern {
    color: #6b7280;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.dropdown-item-modern:hover {
    color: #3b82f6;
    background-color: #f8fafc;
}

.user-avatar-modern {
    width: 24px;
    height: 24px;
    background: #f3f4f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
}

/* Why Choose Us - Minimal Design */
.why-choose-content-modern {
    padding: 2rem 0;
}

.benefit-card-minimal {
    text-align: center;
    padding: 2rem 1rem;
    background: white;
    border-radius: 12px;
    border: 1px solid #f3f4f6;
    transition: all 0.3s ease;
    height: 100%;
}

.benefit-card-minimal:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.benefit-icon-minimal {
    width: 48px;
    height: 48px;
    margin: 0 auto 1rem;
    background: #f8fafc;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #3b82f6;
}

.benefit-title-minimal {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
}

.benefit-desc-minimal {
    font-size: 0.875rem;
    color: #6b7280;
    line-height: 1.6;
    margin: 0;
}

.trust-indicators-minimal {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #f3f4f6;
}

.trust-badges-minimal {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.trust-badge-minimal {
    display: flex;
    align-items: center;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
}

.trust-badge-minimal i {
    color: #10b981;
}

/* Additional Mobile Responsive Updates */
@media (max-width: 768px) {
    .navbar-modern {
        padding: 0.5rem 0;
    }
    
    .brand-logo-modern {
        font-size: 1.125rem;
    }
    
    .brand-icon-modern {
        width: 28px;
        height: 28px;
        font-size: 0.875rem;
    }
    
    .benefit-card-minimal {
        padding: 1.5rem 1rem;
    }
    
    .benefit-icon-minimal {
        width: 40px;
        height: 40px;
        font-size: 1.25rem;
    }
    
    .trust-badges-minimal {
        gap: 1rem;
    }
    
    .trust-badge-minimal {
        font-size: 0.8rem;
    }
}

/* Modern Navbar Styling */
#main-nav {
  background: linear-gradient(135deg, #0057FF 0%, #007BFF 100%);
  min-height: 64px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  padding: 0.75rem 0;
}

#main-nav .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

#main-nav .navbar-brand {
  font-size: 1.5rem;
  letter-spacing: -0.5px;
  color: white !important;
  text-decoration: none;
  display: flex;
  align-items: center;
}

#main-nav .brand-icon {
  width: 32px;
  height: 32px;
  filter: brightness(0) invert(1);
  flex-shrink: 0;
}

#main-nav .brand-text {
  color: white;
  font-weight: 700;
  margin-left: 0.5rem;
}

#main-nav .navbar-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

#main-nav .nav-link {
  font-size: 0.9rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.85) !important;
  padding: 0.75rem 1rem;
  position: relative;
  transition: all 0.3s ease;
  text-decoration: none;
  white-space: nowrap;
}

#main-nav .nav-link:hover,
#main-nav .nav-link.active {
  color: white !important;
  transform: translateY(-1px);
}

#main-nav .nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background-color: white;
  transition: width 0.3s ease;
}

#main-nav .nav-link:hover::after,
#main-nav .nav-link.active::after {
  width: 60%;
}

#main-nav .dropdown-toggle::after {
  display: none;
}

#main-nav .dropdown-menu {
  background: white;
  border: none;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  margin-top: 0.5rem;
  padding: 0.5rem 0;
  min-width: 160px;
}

#main-nav .dropdown-item {
  color: #374151;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

#main-nav .dropdown-item:hover {
  background-color: #f3f4f6;
  color: #0057FF;
  transform: translateX(4px);
}

#main-nav .btn {
  font-weight: 600;
  border-width: 2px;
  padding: 0.625rem 1.25rem;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  border-radius: 8px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

#main-nav .btn-light {
  color: #0057FF;
  background-color: white;
  border-color: white;
}

#main-nav .btn-light:hover {
  background-color: #f8f9fa;
  border-color: #f8f9fa;
  color: #0057FF;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

#main-nav .btn-outline-light {
  color: white;
  border-color: rgba(255, 255, 255, 0.8);
  background-color: transparent;
}

#main-nav .btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: white;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

#main-nav .navbar-toggler {
  border: none;
  padding: 0.25rem 0.5rem;
}

#main-nav .navbar-toggler:focus {
  box-shadow: none;
  outline: none;
}

#main-nav .navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.9%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Mobile Responsive */
@media (max-width: 991.98px) {
  #main-nav .navbar-collapse {
    background: linear-gradient(135deg, #0057FF 0%, #007BFF 100%);
    margin: 1rem -1rem -1rem -1rem;
    padding: 1rem;
    border-radius: 0 0 12px 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  #main-nav .navbar-nav {
    margin-bottom: 1rem;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }
  
  #main-nav .nav-link {
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    width: 100%;
  }
  
  #main-nav .nav-link:last-child {
    border-bottom: none;
  }
  
  #main-nav .nav-link::after {
    display: none;
  }
  
  #main-nav .dropdown-menu {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 0;
    margin-left: 1rem;
    position: static !important;
    transform: none !important;
    width: calc(100% - 1rem);
  }
  
  #main-nav .dropdown-item {
    color: white;
  }
  
  #main-nav .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
  }
  
  #main-nav .d-flex {
    flex-direction: column;
    gap: 0.75rem;
    width: 100%;
  }
  
  #main-nav .btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  #main-nav {
    padding: 0.5rem 0;
  }
  
  #main-nav .navbar-brand {
    font-size: 1.25rem;
  }
  
  #main-nav .brand-icon {
    width: 28px;
    height: 28px;
  }
  
  #main-nav .brand-text {
    margin-left: 0.375rem;
  }
  
  #main-nav .btn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }
}

.modern-top-nav {
    background: transparent;
    z-index: 1051;
    min-height: 60px;
    transition: background 0.3s;
    padding: 0.75rem 1.5rem;
    margin: 0 auto;
    max-width: 100vw;
    width: 100%;
    box-sizing: border-box;
    display: flex !important;
    align-items: center;
    justify-content: center;
}
.modern-top-nav.scrolled {
    background: #111 !important;
}
.modern-top-nav > .d-flex.align-items-center {
    justify-content: center;
    width: 100%;
}
.modern-top-nav .nav-link-minimal,
.modern-top-nav .dropdown-toggle {
    padding: 0.75rem 1.25rem;
    margin: 0 0.25rem;
    border-radius: 8px;
    text-align: center;
}
.modern-top-nav .dropdown-menu {
    min-width: 180px;
}
.modern-top-nav .btn {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
}
@media (max-width: 991.98px) {
    .modern-top-nav {
        padding: 0.5rem 0.5rem;
    }
    .modern-top-nav .nav-link-minimal,
    .modern-top-nav .dropdown-toggle {
        padding: 0.5rem 0.75rem;
        font-size: 0.95rem;
    }
    .modern-top-nav .btn {
        padding: 0.5rem 1rem;
        font-size: 0.95rem;
    }
}
@media (max-width: 767.98px) {
    .modern-top-nav {
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 0.25rem;
        min-height: 56px;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        width: 100vw;
        margin: 0;
        border-radius: 0;
    }
    .modern-top-nav > .d-flex.align-items-center {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
        justify-content: center;
        align-items: center;
    }
    .modern-top-nav .nav-link-minimal,
    .modern-top-nav .dropdown-toggle {
        width: 100%;
        margin: 0.15rem 0;
        padding: 0.5rem 0.5rem;
        text-align: center;
    }
    .modern-top-nav .dropdown-menu {
        left: 50% !important;
        transform: translateX(-50%) !important;
        min-width: 160px;
    }
    .modern-top-nav .btn {
        width: 100%;
        margin: 0.25rem 0;
        padding: 0.5rem 0.5rem;
        font-size: 1rem;
    }
}