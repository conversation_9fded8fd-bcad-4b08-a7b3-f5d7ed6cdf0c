<?php
require_once 'includes/db.php';

try {
    // Add missing columns to users table
    $alterQueries = [
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS status ENUM('active', 'banned') NOT NULL DEFAULT 'active'",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_completed BOOLEAN NOT NULL DEFAULT 0",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_picture VARCHAR(255) DEFAULT NULL",
        "ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login TIMESTAMP DEFAULT NULL",
        "ALTER TABLE users MODIFY COLUMN password VARCHAR(255) NULL" // Make password nullable for OAuth users
    ];
    
    foreach ($alterQueries as $query) {
        $pdo->exec($query);
        echo "Executed: " . $query . "\n";
    }
    
    echo "Database updated successfully!\n";
    
} catch (Exception $e) {
    echo "Error updating database: " . $e->getMessage() . "\n";
}
?> 