<?php
// update_claimed_at.php
require_once __DIR__ . '/includes/db.php';

try {
    // Find reviews with status 'claimed' and claimed_at is NULL
    $stmt = $pdo->prepare("SELECT id, created_at FROM reviews WHERE status = 'claimed' AND claimed_at IS NULL");
    $stmt->execute();
    $reviews = $stmt->fetchAll();

    if (empty($reviews)) {
        echo "No reviews need updating.\n";
    } else {
        $update = $pdo->prepare("UPDATE reviews SET claimed_at = ? WHERE id = ?");
        foreach ($reviews as $review) {
            $update->execute([$review['created_at'], $review['id']]);
            echo "Updated review ID {$review['id']} with claimed_at = {$review['created_at']}\n";
        }
        echo "Update complete.\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
} 