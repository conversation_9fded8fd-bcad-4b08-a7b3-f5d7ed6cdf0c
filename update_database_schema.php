<?php
// update_database_schema.php
require_once __DIR__ . '/includes/db.php';

try {
    // 1. Add claimed_at to reviews if not exists
    $result = $pdo->query("SHOW COLUMNS FROM reviews LIKE 'claimed_at'");
    if ($result->rowCount() === 0) {
        $pdo->exec("ALTER TABLE reviews ADD COLUMN claimed_at TIMESTAMP DEFAULT NULL AFTER created_at");
        echo "Added 'claimed_at' column to 'reviews' table.\n";
    } else {
        echo "'claimed_at' column already exists in 'reviews' table.\n";
    }

    // 2. Add any other missing columns or tables as needed (extend as your schema grows)
    // Example: Check for upi_id in users
    $result = $pdo->query("SHOW COLUMNS FROM users LIKE 'upi_id'");
    if ($result->rowCount() === 0) {
        $pdo->exec("ALTER TABLE users ADD COLUMN upi_id VARCHAR(100) DEFAULT NULL");
        echo "Added 'upi_id' column to 'users' table.\n";
    } else {
        echo "'upi_id' column already exists in 'users' table.\n";
    }

    // Example: Check for upi_qr in users
    $result = $pdo->query("SHOW COLUMNS FROM users LIKE 'upi_qr'");
    if ($result->rowCount() === 0) {
        $pdo->exec("ALTER TABLE users ADD COLUMN upi_qr VARCHAR(255) DEFAULT NULL");
        echo "Added 'upi_qr' column to 'users' table.\n";
    } else {
        echo "'upi_qr' column already exists in 'users' table.\n";
    }

    // Example: Check for whatsapp in users
    $result = $pdo->query("SHOW COLUMNS FROM users LIKE 'whatsapp'");
    if ($result->rowCount() === 0) {
        $pdo->exec("ALTER TABLE users ADD COLUMN whatsapp VARCHAR(20) DEFAULT NULL");
        echo "Added 'whatsapp' column to 'users' table.\n";
    } else {
        echo "'whatsapp' column already exists in 'users' table.\n";
    }

    // Example: Check for profile_completed in users
    $result = $pdo->query("SHOW COLUMNS FROM users LIKE 'profile_completed'");
    if ($result->rowCount() === 0) {
        $pdo->exec("ALTER TABLE users ADD COLUMN profile_completed TINYINT(1) NOT NULL DEFAULT 0");
        echo "Added 'profile_completed' column to 'users' table.\n";
    } else {
        echo "'profile_completed' column already exists in 'users' table.\n";
    }

    echo "Database schema update complete.\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
} 