-- Database schema updates for enhanced reviewer dashboard

-- Add columns for 15-minute timer functionality
ALTER TABLE reviews 
ADD COLUMN claimed_at TIMESTAMP NULL DEFAULT NULL AFTER reviewer_id;

ALTER TABLE reviews 
ADD COLUMN completed_at TIMESTAMP NULL DEFAULT NULL AFTER claimed_at;

-- Add columns for proof submission
ALTER TABLE reviews 
ADD COLUMN proof_screenshot VARCHAR(255) NULL DEFAULT NULL AFTER completed_at;

ALTER TABLE reviews 
ADD COLUMN proof_link TEXT NULL DEFAULT NULL AFTER proof_screenshot;

ALTER TABLE reviews 
ADD COLUMN proof_submitted_at TIMESTAMP NULL DEFAULT NULL AFTER proof_link;

-- Add column for payment tracking in wallet system
ALTER TABLE reviews 
ADD COLUMN paid TINYINT(1) DEFAULT 0 AFTER proof_submitted_at;

-- Add indexes for better performance
CREATE INDEX idx_reviews_status_reviewer ON reviews(status, reviewer_id);
CREATE INDEX idx_reviews_claimed_at ON reviews(claimed_at);
CREATE INDEX idx_reviews_completed_at ON reviews(completed_at);
CREATE INDEX idx_reviews_timer_expire ON reviews(status, claimed_at);

-- Update existing claimed reviews to have claimed_at timestamp
UPDATE reviews 
SET claimed_at = created_at 
WHERE status = 'claimed' AND claimed_at IS NULL;

-- Update existing completed reviews to have completed_at timestamp  
UPDATE reviews 
SET completed_at = updated_at 
WHERE status IN ('completed', 'approved') AND completed_at IS NULL; 