<?php
require_once '../includes/auth.php';
require_login();
$user = current_user();

if ($user['role'] !== 'reviewer') {
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}
require_once '../includes/db.php';

header('Content-Type: application/json');

if (!isset($_GET['id'])) {
    echo json_encode(['success' => false, 'error' => 'Review ID required']);
    exit;
}

$review_id = intval($_GET['id']);

// Fetch review details (only if claimed by current user)
$stmt = $pdo->prepare('
    SELECT r.*, u.name as customer_name 
    FROM reviews r 
    JOIN users u ON r.customer_id = u.id 
    WHERE r.id = ? AND r.reviewer_id = ? AND r.status = "claimed"
');
$stmt->execute([$review_id, $user['id']]);
$review = $stmt->fetch();

if (!$review) {
    echo json_encode(['success' => false, 'error' => 'Review not found or not claimed by you']);
    exit;
}

// Check if review is expired (more than 30 minutes)
$claimed_time = new DateTime($review['created_at']);
$now = new DateTime();
$diff = $now->diff($claimed_time);
$minutes_elapsed = $diff->i + ($diff->h * 60) + ($diff->days * 24 * 60);

if ($minutes_elapsed > 30) {
    // Release the review back to available
    $stmt = $pdo->prepare('UPDATE reviews SET reviewer_id = NULL, status = "open" WHERE id = ?');
    $stmt->execute([$review_id]);
    
    echo json_encode(['success' => false, 'error' => 'Review time expired']);
    exit;
}

echo json_encode(['success' => true, 'review' => $review]);
?> 