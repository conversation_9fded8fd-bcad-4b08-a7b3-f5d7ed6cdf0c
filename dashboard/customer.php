<?php
require_once '../includes/auth.php';
require_login();
$user = current_user();
if ($user['role'] !== 'customer') {
    header('Location: /index.php');
    exit;
}
require_once '../includes/db.php';

// Flash message helper
function set_flash($msg) { $_SESSION['flash'] = $msg; }
function get_flash() { $msg = $_SESSION['flash'] ?? ''; unset($_SESSION['flash']); return $msg; }

// Handle balance top-up
if (isset($_POST['add_balance'])) {
    $amount = floatval($_POST['balance_amount']);
    if ($amount > 0) {
        $pdo->beginTransaction();
        try {
            $stmt = $pdo->prepare('UPDATE users SET wallet_balance = wallet_balance + ? WHERE id = ?');
            $stmt->execute([$amount, $user['id']]);
            $stmt = $pdo->prepare('INSERT INTO wallet_transactions (user_id, type, amount, reference) VALUES (?, "topup", ?, ?)');
            $stmt->execute([$user['id'], $amount, 'Balance top-up']);
            $pdo->commit();
            set_flash('<div class="alert alert-success">Balance added successfully!</div>');
            $stmt = $pdo->prepare('SELECT * FROM users WHERE id = ?');
            $stmt->execute([$user['id']]);
            $user = $stmt->fetch();
        } catch (Exception $e) {
            $pdo->rollBack();
            set_flash('<div class="alert alert-danger">Error adding balance. Please try again.</div>');
        }
    }
    header('Location: customer.php');
    exit;
}

// Handle review post
if (isset($_POST['post_reviews'])) {
    $platform = trim($_POST['platform'] ?? '');
    $business_name = trim($_POST['business_name'] ?? '');
    $business_link = trim($_POST['business_link'] ?? '');
    $country = trim($_POST['country'] ?? '');
    $total_amount = 0;
    $reviews_data = [];
    
    // Collect all reviews
    for ($i = 0; $i < 10; $i++) {
        if (!empty($_POST['review_text_' . $i])) {
            $reviews_data[] = [
                'text' => trim($_POST['review_text_' . $i]),
                'amount' => floatval($_POST['review_amount_' . $i])
            ];
            $total_amount += floatval($_POST['review_amount_' . $i]);
        }
    }
    
    // Handle screenshot upload
    $screenshot_url = null;
    if (!empty($_FILES['screenshot']['name'])) {
        $target_dir = __DIR__ . '/../uploads/';
        if (!is_dir($target_dir)) mkdir($target_dir, 0777, true);
        $filename = uniqid('ss_') . '_' . basename($_FILES['screenshot']['name']);
        $target_file = $target_dir . $filename;
        if (move_uploaded_file($_FILES['screenshot']['tmp_name'], $target_file)) {
            $screenshot_url = '/uploads/' . $filename;
        }
    }
    
    if ($platform && $business_name && !empty($reviews_data) && $screenshot_url) {
        // Validate minimum prices against service settings
        $stmt = $pdo->prepare('SELECT min_price, max_price FROM services WHERE platform_name = ? AND is_active = 1');
        $stmt->execute([$platform]);
        $service = $stmt->fetch();
        
        if ($service) {
            $validation_errors = [];
            foreach ($reviews_data as $index => $review) {
                if ($review['amount'] < $service['min_price']) {
                    $validation_errors[] = "Review " . ($index + 1) . ": Amount must be at least $" . number_format($service['min_price'], 2) . " for " . ucfirst($platform) . " reviews";
                }
                if ($review['amount'] > $service['max_price']) {
                    $validation_errors[] = "Review " . ($index + 1) . ": Amount cannot exceed $" . number_format($service['max_price'], 2) . " for " . ucfirst($platform) . " reviews";
                }
            }
            
            if (!empty($validation_errors)) {
                set_flash('<div class="alert alert-danger"><strong>Price Validation Errors:</strong><br>' . implode('<br>', $validation_errors) . '</div>');
                header('Location: customer.php');
                exit;
            }
        }
        
        if ($user['wallet_balance'] < $total_amount) {
            set_flash('<div class="alert alert-danger">Insufficient wallet balance. You need $' . number_format($total_amount, 2) . ' but have $' . number_format($user['wallet_balance'], 2) . '</div>');
        } else {
            $pdo->beginTransaction();
            try {
                // Deduct total amount
                $stmt = $pdo->prepare('UPDATE users SET wallet_balance = wallet_balance - ? WHERE id = ?');
                $stmt->execute([$total_amount, $user['id']]);
                
                // Create reviews
                foreach ($reviews_data as $review) {
                    $stmt = $pdo->prepare('INSERT INTO reviews (customer_id, platform, business_name, business_link, country, review_text, amount, status, screenshot_url) VALUES (?, ?, ?, ?, ?, ?, ?, "open", ?)');
                    $stmt->execute([$user['id'], $platform, $business_name, $business_link, $country, $review['text'], $review['amount'], $screenshot_url]);
                    $review_id = $pdo->lastInsertId();
                    
                    // Record transaction
                    $stmt = $pdo->prepare('INSERT INTO wallet_transactions (user_id, type, amount, review_id, reference) VALUES (?, "debit", ?, ?, ?)');
                    $stmt->execute([$user['id'], $review['amount'], $review_id, 'Review posted']);
                }
                
                $pdo->commit();
                set_flash('<div class="alert alert-success">' . count($reviews_data) . ' review(s) posted successfully! Total amount: $' . number_format($total_amount, 2) . '</div>');
                $stmt = $pdo->prepare('SELECT * FROM users WHERE id = ?');
                $stmt->execute([$user['id']]);
                $user = $stmt->fetch();
            } catch (Exception $e) {
                $pdo->rollBack();
                error_log('Review posting error: ' . $e->getMessage());
                set_flash('<div class="alert alert-danger">Error posting reviews: ' . $e->getMessage() . '</div>');
            }
        }
    } else {
        $errors = [];
        if (!$platform) $errors[] = 'Platform is required';
        if (!$business_name) $errors[] = 'Business name is required';
        if (empty($reviews_data)) $errors[] = 'At least one review is required';
        if (!$screenshot_url) $errors[] = 'Business profile screenshot is mandatory';
        set_flash('<div class="alert alert-danger">' . implode(', ', $errors) . '</div>');
    }
    header('Location: customer.php');
    exit;
}

// Fetch previous reviews
$stmt = $pdo->prepare('SELECT * FROM reviews WHERE customer_id = ? ORDER BY created_at DESC');
$stmt->execute([$user['id']]);
$reviews = $stmt->fetchAll();
$message = get_flash();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Dashboard - 1xreviews</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/6.6.6/css/flag-icons.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --bg-light: #f8fafc;
            --border-color: #e5e7eb;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-light);
            line-height: 1.6;
        }
        
        .navbar {
            background: white !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }
        
        .main-content {
            padding-top: 80px;
        }
        
        .card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .card-header {
            background: white;
            border-bottom: 1px solid var(--border-color);
            border-radius: 16px 16px 0 0 !important;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .btn-primary {
            background: var(--primary-color);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }
        
        .btn-success {
            background: var(--success-color);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-success:hover {
            background: #059669;
            transform: translateY(-2px);
        }
        
        .wallet-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .wallet-amount {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }
        
        .platform-option {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .platform-option:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .platform-option.selected {
            border-color: var(--primary-color);
            background: #eff6ff;
        }
        
        .platform-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }
        
        .platform-name {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.25rem;
        }
        
        .platform-desc {
            font-size: 0.875rem;
            color: var(--text-light);
        }
        
        .country-flag {
            width: 40px;
            height: 30px;
            border-radius: 4px;
            margin-right: 0.5rem;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        
        .country-flag:hover {
            transform: scale(1.1);
        }
        
        .country-flag.selected {
            border: 3px solid var(--primary-color);
        }
        
        .review-item {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: white;
            position: relative;
        }
        
        .review-item:hover {
            border-color: var(--primary-color);
        }
        
        .remove-review {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: var(--danger-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .remove-review:hover {
            background: #dc2626;
            transform: scale(1.1);
        }
        
        .add-review-btn {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .add-review-btn:hover {
            border-color: var(--primary-color);
            background: #eff6ff;
        }
        
        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .screenshot-upload {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: white;
        }
        
        .screenshot-upload:hover {
            border-color: var(--primary-color);
            background: #eff6ff;
        }
        
        .screenshot-preview {
            max-width: 200px;
            max-height: 150px;
            border-radius: 8px;
            margin-top: 1rem;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-open { background: #fef3c7; color: #92400e; }
        .status-claimed { background: #dbeafe; color: #1e40af; }
        .status-submitted { background: #d1fae5; color: #065f46; }
        .status-completed { background: #dcfce7; color: #166534; }
        .status-approved { background: #bbf7d0; color: #15803d; }
        .status-rejected { background: #fecaca; color: #991b1b; }
        
        .table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .table th {
            background: var(--bg-light);
            border: none;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .table td {
            border: none;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }
        
        @media (max-width: 768px) {
            .wallet-amount {
                font-size: 2rem;
            }
            
            .platform-option {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-star me-2"></i>1xreviews
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($user['name']); ?>
                </span>
                <a class="nav-link me-3" href="my_account.php">
                    <i class="fas fa-cog me-2"></i>My Account
                </a>
                <a class="nav-link" href="/logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="main-content">
        <div class="container py-4">
            <?php echo $message; ?>
            
            <!-- Wallet Balance Card -->
            <div class="wallet-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-2">Wallet Balance</h4>
                        <div class="wallet-amount">$
<?php 
$balance = isset($user['wallet_balance']) && $user['wallet_balance'] !== null ? $user['wallet_balance'] : 0.00;
echo number_format((float)$balance, 2);
?>
</div>
                        <p class="mb-0">Add funds to post review requests</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#addBalanceModal">
                            <i class="fas fa-plus me-2"></i>Add Balance
                        </button>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Post Reviews Form -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Post Review Requests</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" enctype="multipart/form-data" id="reviewForm">
                                <!-- Platform Selection -->
                                <div class="mb-4">
                                    <label class="form-label fw-bold">Select Platform *</label>
                                    <div class="row g-3">
                                        <div class="col-md-4">
                                            <div class="platform-option" data-platform="google">
                                                <div class="platform-icon">
                                                    <i class="fab fa-google"></i>
                                                </div>
                                                <div class="platform-name">Google My Business</div>
                                                <div class="platform-desc">Google Reviews</div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="platform-option" data-platform="yelp">
                                                <div class="platform-icon">
                                                    <i class="fab fa-yelp"></i>
                                                </div>
                                                <div class="platform-name">Yelp</div>
                                                <div class="platform-desc">Business Reviews</div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="platform-option" data-platform="facebook">
                                                <div class="platform-icon">
                                                    <i class="fab fa-facebook"></i>
                                                </div>
                                                <div class="platform-name">Facebook</div>
                                                <div class="platform-desc">Page Reviews</div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="platform-option" data-platform="tripadvisor">
                                                <div class="platform-icon">
                                                    <i class="fas fa-plane"></i>
                                                </div>
                                                <div class="platform-name">TripAdvisor</div>
                                                <div class="platform-desc">Travel Reviews</div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="platform-option" data-platform="trustpilot">
                                                <div class="platform-icon">
                                                    <i class="fas fa-shield-alt"></i>
                                                </div>
                                                <div class="platform-name">Trustpilot</div>
                                                <div class="platform-desc">Trust Reviews</div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="platform-option" data-platform="other">
                                                <div class="platform-icon">
                                                    <i class="fas fa-globe"></i>
                                                </div>
                                                <div class="platform-name">Other</div>
                                                <div class="platform-desc">Custom Platform</div>
                                            </div>
                                        </div>
                                    </div>
                                    <input type="hidden" name="platform" id="selectedPlatform" required>
                                </div>

                                <!-- Business Information -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label class="form-label fw-bold">Business Name *</label>
                                        <input type="text" name="business_name" class="form-control" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label fw-bold">Business Profile Link</label>
                                        <input type="url" name="business_link" class="form-control" placeholder="https://...">
                                    </div>
                                </div>

                                <!-- Country Selection -->
                                <div class="mb-4">
                                    <label class="form-label fw-bold">Select Country *</label>
                                    <div class="d-flex flex-wrap gap-2">
                                        <img src="https://flagcdn.com/w40/in.png" class="country-flag" data-country="India" alt="India">
                                        <img src="https://flagcdn.com/w40/ca.png" class="country-flag" data-country="Canada" alt="Canada">
                                        <img src="https://flagcdn.com/w40/au.png" class="country-flag" data-country="Australia" alt="Australia">
                                        <img src="https://flagcdn.com/w40/us.png" class="country-flag" data-country="USA" alt="USA">
                                        <img src="https://flagcdn.com/w40/gb.png" class="country-flag" data-country="UK" alt="UK">
                                        <img src="https://flagcdn.com/w40/de.png" class="country-flag" data-country="Germany" alt="Germany">
                                        <img src="https://flagcdn.com/w40/fr.png" class="country-flag" data-country="France" alt="France">
                                        <img src="https://flagcdn.com/w40/jp.png" class="country-flag" data-country="Japan" alt="Japan">
                                        <img src="https://flagcdn.com/w40/sg.png" class="country-flag" data-country="Singapore" alt="Singapore">
                                        <img src="https://flagcdn.com/w40/ae.png" class="country-flag" data-country="UAE" alt="UAE">
                                    </div>
                                    <input type="hidden" name="country" id="selectedCountry" required>
                                </div>

                                <!-- Screenshot Upload -->
                                <div class="mb-4">
                                    <label class="form-label fw-bold">Business Profile Screenshot *</label>
                                    <div class="screenshot-upload" onclick="document.getElementById('screenshot').click()">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <h5>Upload Screenshot</h5>
                                        <p class="text-muted">Click to upload or drag and drop</p>
                                        <p class="text-muted small">PNG, JPG up to 5MB</p>
                                    </div>
                                    <input type="file" name="screenshot" id="screenshot" class="d-none" accept="image/*" required>
                                    <div id="screenshotPreview"></div>
                                </div>

                                <!-- Reviews Section -->
                                <div class="mb-4">
                                    <label class="form-label fw-bold">Review Content</label>
                                    <div id="reviewsContainer">
                                        <div class="review-item" data-review="0">
                                            <button type="button" class="remove-review" onclick="removeReview(0)" style="display: none;">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <label class="form-label">Review Text *</label>
                                                    <textarea name="review_text_0" class="form-control" rows="2" placeholder="Write your review content here..." required></textarea>
                                                </div>
                                                <div class="col-md-4">
                                                    <label class="form-label">Amount ($) *</label>
                                                    <input type="number" name="review_amount_0" class="form-control" min="1" step="0.01" placeholder="25.00" required>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="add-review-btn" onclick="addReview()">
                                        <i class="fas fa-plus-circle fa-2x text-primary mb-2"></i>
                                        <h5>Add Another Review</h5>
                                        <p class="text-muted">Click to add more reviews</p>
                                    </div>
                                </div>

                                <button type="submit" name="post_reviews" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Post Reviews
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Recent Reviews -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Reviews</h5>
                        </div>
                        <div class="card-body p-0">
                            <?php if (empty($reviews)): ?>
                                <div class="p-4 text-center text-muted">
                                    <i class="fas fa-inbox fa-3x mb-3"></i>
                                    <h6>No reviews posted yet</h6>
                                    <p class="small">Start by posting your first review request</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>Platform</th>
                                                <th>Business</th>
                                                <th>Amount</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach (array_slice($reviews, 0, 5) as $r): ?>
                                                <tr>
                                                    <td>
                                                        <i class="fab fa-<?php echo strtolower($r['platform']); ?> me-2"></i>
                                                        <?php echo htmlspecialchars($r['platform']); ?>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($r['business_name']); ?></td>
                                                    <td>$<?php echo number_format($r['amount'], 2); ?></td>
                                                    <td>
                                                        <span class="status-badge status-<?php echo $r['status']; ?>">
                                                            <?php echo ucfirst($r['status']); ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php if (count($reviews) > 5): ?>
                                    <div class="p-3 text-center">
                                        <a href="#" class="btn btn-outline-primary btn-sm">View All Reviews</a>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Balance Modal -->
    <div class="modal fade" id="addBalanceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Balance</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Amount ($)</label>
                            <input type="number" name="balance_amount" class="form-control" min="1" step="0.01" placeholder="100.00" required>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Add funds to your wallet to post review requests. You can add any amount starting from $1.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="add_balance" class="btn btn-success">Add Balance</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let reviewCount = 1;
        
        // Platform selection
        document.querySelectorAll('.platform-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.platform-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                document.getElementById('selectedPlatform').value = this.dataset.platform;
                
                // Update price validation for the selected platform
                updatePriceValidation(this.dataset.platform);
            });
        });
        
        // Country selection
        document.querySelectorAll('.country-flag').forEach(flag => {
            flag.addEventListener('click', function() {
                document.querySelectorAll('.country-flag').forEach(f => f.classList.remove('selected'));
                this.classList.add('selected');
                document.getElementById('selectedCountry').value = this.dataset.country;
            });
        });
        
        // Screenshot upload
        document.getElementById('screenshot').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('screenshotPreview').innerHTML = 
                        `<img src="${e.target.result}" class="screenshot-preview" alt="Preview">`;
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Add review
        function addReview() {
            const container = document.getElementById('reviewsContainer');
            const reviewItem = document.createElement('div');
            reviewItem.className = 'review-item';
            reviewItem.dataset.review = reviewCount;
            reviewItem.innerHTML = `
                <button type="button" class="remove-review" onclick="removeReview(${reviewCount})">
                    <i class="fas fa-times"></i>
                </button>
                <div class="row">
                    <div class="col-md-8">
                        <label class="form-label">Review Text *</label>
                        <textarea name="review_text_${reviewCount}" class="form-control" rows="2" placeholder="Write your review content here..." required></textarea>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Amount ($) *</label>
                        <input type="number" name="review_amount_${reviewCount}" class="form-control" min="1" step="0.01" placeholder="25.00" required>
                    </div>
                </div>
            `;
            container.appendChild(reviewItem);
            
            // Apply price validation to the new amount input
            const selectedPlatform = document.getElementById('selectedPlatform').value;
            if (selectedPlatform) {
                const pricing = platformPricing[selectedPlatform];
                if (pricing) {
                    const newInput = reviewItem.querySelector(`input[name="review_amount_${reviewCount}"]`);
                    newInput.min = pricing.min;
                    newInput.max = pricing.max;
                    newInput.placeholder = pricing.default.toFixed(2);
                    
                    newInput.addEventListener('input', function() {
                        validatePrice(this, pricing);
                    });
                }
            }
            
            reviewCount++;
        }
        
        // Remove review
        function removeReview(index) {
            const reviewItem = document.querySelector(`[data-review="${index}"]`);
            if (reviewItem) {
                reviewItem.remove();
            }
        }
        
        // Form validation
        document.getElementById('reviewForm').addEventListener('submit', function(e) {
            const platform = document.getElementById('selectedPlatform').value;
            const country = document.getElementById('selectedCountry').value;
            const screenshot = document.getElementById('screenshot').files[0];
            
            if (!platform) {
                alert('Please select a platform');
                e.preventDefault();
                return;
            }
            
            if (!country) {
                alert('Please select a country');
                e.preventDefault();
                return;
            }
            
            if (!screenshot) {
                alert('Business profile screenshot is mandatory');
                e.preventDefault();
                return;
            }
        });

        // Price validation data (you can fetch this via AJAX if needed)
        const platformPricing = {
            'google': { min: 15.00, max: 50.00, default: 25.00 },
            'trustpilot': { min: 20.00, max: 60.00, default: 30.00 },
            'tripadvisor': { min: 10.00, max: 40.00, default: 20.00 },
            'clutch': { min: 25.00, max: 80.00, default: 35.00 },
            'yelp': { min: 12.00, max: 45.00, default: 22.00 },
            'facebook': { min: 8.00, max: 35.00, default: 18.00 },
            'amazon': { min: 5.00, max: 30.00, default: 15.00 }
        };
        
        function updatePriceValidation(platform) {
            const pricing = platformPricing[platform];
            if (!pricing) return;
            
            // Update all amount inputs
            document.querySelectorAll('input[name^="review_amount_"]').forEach(input => {
                input.min = pricing.min;
                input.max = pricing.max;
                input.placeholder = pricing.default.toFixed(2);
                
                // Add validation feedback
                input.addEventListener('input', function() {
                    validatePrice(this, pricing);
                });
                
                // Validate current value
                validatePrice(input, pricing);
            });
            
            // Show pricing info
            showPricingInfo(platform, pricing);
        }
        
        function validatePrice(input, pricing) {
            const value = parseFloat(input.value);
            const feedback = input.parentNode.querySelector('.price-feedback');
            
            if (feedback) {
                feedback.remove();
            }
            
            if (isNaN(value)) return;
            
            const newFeedback = document.createElement('div');
            newFeedback.className = 'price-feedback mt-1';
            
            if (value < pricing.min) {
                newFeedback.innerHTML = `<small class="text-danger"><i class="fas fa-exclamation-triangle"></i> Minimum price for this platform is $${pricing.min.toFixed(2)}</small>`;
                input.classList.add('is-invalid');
            } else if (value > pricing.max) {
                newFeedback.innerHTML = `<small class="text-danger"><i class="fas fa-exclamation-triangle"></i> Maximum price for this platform is $${pricing.max.toFixed(2)}</small>`;
                input.classList.add('is-invalid');
            } else {
                newFeedback.innerHTML = `<small class="text-success"><i class="fas fa-check"></i> Price is within valid range</small>`;
                input.classList.remove('is-invalid');
                input.classList.add('is-valid');
            }
            
            input.parentNode.appendChild(newFeedback);
        }
        
        function showPricingInfo(platform, pricing) {
            // Remove existing pricing info
            const existingInfo = document.querySelector('.pricing-info');
            if (existingInfo) {
                existingInfo.remove();
            }
            
            // Add pricing info after platform selection
            const platformSection = document.querySelector('.platform-selection');
            if (platformSection) {
                const infoDiv = document.createElement('div');
                infoDiv.className = 'pricing-info alert alert-info mt-3';
                infoDiv.innerHTML = `
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>${platform.charAt(0).toUpperCase() + platform.slice(1)} Review Pricing:</strong>
                    <br>Minimum: $${pricing.min.toFixed(2)} | Maximum: $${pricing.max.toFixed(2)} | Recommended: $${pricing.default.toFixed(2)}
                `;
                platformSection.appendChild(infoDiv);
            }
        }
    </script>
</body>
</html>
