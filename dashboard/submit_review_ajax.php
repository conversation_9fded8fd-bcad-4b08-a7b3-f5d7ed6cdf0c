<?php
require_once '../includes/auth.php';
require_login();
$user = current_user();
if ($user['role'] !== 'reviewer') {
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}
require_once '../includes/db.php';

header('Content-Type: application/json');

if (!isset($_POST['review_id']) || !isset($_FILES['review_screenshot'])) {
    echo json_encode(['success' => false, 'error' => 'Missing required data']);
    exit;
}

$review_id = intval($_POST['review_id']);
$review_url = trim($_POST['review_url'] ?? '');

// Verify the review belongs to current user and is claimed
$stmt = $pdo->prepare('SELECT * FROM reviews WHERE id = ? AND reviewer_id = ? AND status = "claimed"');
$stmt->execute([$review_id, $user['id']]);
$review = $stmt->fetch();

if (!$review) {
    echo json_encode(['success' => false, 'error' => 'Review not found or not claimed by you']);
    exit;
}

// Check if review is expired (more than 30 minutes)
$claimed_time = new DateTime($review['claimed_at']);
$now = new DateTime();
$diff = $now->diff($claimed_time);
$minutes_elapsed = $diff->i + ($diff->h * 60) + ($diff->days * 24 * 60);

if ($minutes_elapsed > 30) {
    // Release the review back to available
    $stmt = $pdo->prepare('UPDATE reviews SET reviewer_id = NULL, status = "open", claimed_at = NULL WHERE id = ?');
    $stmt->execute([$review_id]);
    
    echo json_encode(['success' => false, 'error' => 'Review time expired']);
    exit;
}

// Handle screenshot upload
$screenshot_url = null;
if (!empty($_FILES['review_screenshot']['name'])) {
    $target_dir = __DIR__ . '/../uploads/';
    if (!is_dir($target_dir)) mkdir($target_dir, 0777, true);
    $filename = uniqid('review_') . '_' . basename($_FILES['review_screenshot']['name']);
    $target_file = $target_dir . $filename;
    if (move_uploaded_file($_FILES['review_screenshot']['tmp_name'], $target_file)) {
        $screenshot_url = '/uploads/' . $filename;
    }
}

if (!$screenshot_url) {
    echo json_encode(['success' => false, 'error' => 'Please upload a screenshot']);
    exit;
}

$pdo->beginTransaction();
try {
    // Update review status and add screenshot
    $new_screenshot_url = $review['screenshot_url'] ? $review['screenshot_url'] . '|' . $screenshot_url : $screenshot_url;
    
    $stmt = $pdo->prepare('UPDATE reviews SET status = "submitted", screenshot_url = ? WHERE id = ? AND reviewer_id = ?');
    $stmt->execute([$new_screenshot_url, $review_id, $user['id']]);
    
    $pdo->commit();
    echo json_encode(['success' => true, 'message' => 'Review submitted successfully!']);
    
} catch (Exception $e) {
    $pdo->rollBack();
    error_log('Review submission error: ' . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'Error submitting review. Please try again.']);
}
?> 