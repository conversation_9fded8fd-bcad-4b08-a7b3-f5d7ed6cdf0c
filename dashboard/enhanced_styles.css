/* Enhanced Wallet Summary Styles */
.wallet-stat-card {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
}

.wallet-stat-card:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.wallet-stat-icon {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.5rem;
    font-size: 1rem;
    color: white;
}

.wallet-stat-amount {
    font-size: 1.25rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.25rem;
}

.wallet-stat-label {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.withdrawable-card {
    background: linear-gradient(135deg, var(--accent-color), #d97706);
    color: white;
    border-radius: 16px;
    padding: 2rem 1.5rem;
    height: 100%;
    display: flex;
    align-items: center;
}

.withdrawable-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
}

.withdrawable-amount {
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
}

/* Filter Tabs Styles */
.review-filter-tabs {
    display: flex;
    gap: 0.5rem;
}

.filter-tab {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-light);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-tab:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.filter-tab.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Enhanced Review Cards */
.enhanced-review-card {
    transition: all 0.3s ease;
}

.enhanced-review-card.filtered-out {
    display: none;
}

.enhanced-review-card[data-status="expired"] {
    opacity: 0.6;
    border-color: var(--danger-color);
}

.enhanced-review-card[data-status="completed"] {
    border-color: var(--success-color);
    background: #f0fdf4;
}

/* 15-Minute Timer Styles */
.timer-badge-15min {
    background: var(--warning-color);
    color: white;
    padding: 6px 10px;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 4px;
}

.timer-badge-15min.expired {
    background: var(--danger-color);
    animation: pulse 1s infinite;
}

.timer-text-15min {
    font-family: 'Courier New', monospace;
    font-weight: 700;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Proof Submission Styles */
.proof-upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fafafa;
}

.proof-upload-area:hover {
    border-color: var(--primary-color);
    background: #eff6ff;
}

.proof-status-card {
    background: #f8fafc;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1rem;
}

/* Weekly Leaderboard Styles */
.leaderboard-list {
    padding: 0;
}

.leaderboard-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background 0.3s ease;
}

.leaderboard-item:last-child {
    border-bottom: none;
}

.leaderboard-item:hover {
    background: #f8fafc;
}

.leaderboard-item.top-performer {
    background: linear-gradient(135deg, #fef3c7, #fbbf24);
}

.rank-badge {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 1rem;
    font-size: 0.875rem;
}

.rank-badge.rank-1 {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
    font-size: 1rem;
}

.rank-badge.rank-2 {
    background: linear-gradient(135deg, #e5e7eb, #9ca3af);
    color: white;
}

.rank-badge.rank-3 {
    background: linear-gradient(135deg, #f97316, #ea580c);
    color: white;
}

.rank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
    background: #f3f4f6;
    color: var(--text-dark);
}

.leader-info {
    flex: 1;
}

.leader-name {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
}

.leader-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
}

.reviews-count {
    color: var(--text-light);
}

.earnings-amount {
    color: var(--success-color);
    font-weight: 600;
}

/* Enhanced Earnings List */
.earnings-list {
    padding: 0;
}

.earning-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
    transition: background 0.3s ease;
}

.earning-item:last-child {
    border-bottom: none;
}

.earning-item:hover {
    background: #f8fafc;
}

.earning-platform {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
}

.platform-icon-sm {
    font-size: 1.25rem;
    color: var(--primary-color);
}

.earning-details {
    flex: 1;
}

.earning-business {
    font-weight: 500;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.earning-date {
    font-size: 0.75rem;
    color: var(--text-light);
}

.earning-amount {
    font-weight: 700;
    color: var(--success-color);
    font-size: 1.1rem;
}

/* New Review Notification */
#newReviewNotification {
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1050;
    min-width: 400px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .wallet-stat-card {
        margin-bottom: 0.5rem;
    }
    
    .withdrawable-amount {
        font-size: 1.5rem;
    }
    
    .review-filter-tabs {
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .filter-tab {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }
    
    .timer-badge-15min {
        padding: 4px 8px;
        font-size: 0.75rem;
    }
    
    .leaderboard-item {
        padding: 0.75rem;
    }
    
    .rank-badge {
        width: 32px;
        height: 32px;
        font-size: 0.75rem;
        margin-right: 0.75rem;
    }
    
    .leader-stats {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .earning-item {
        padding: 0.75rem;
    }
    
    .earning-platform {
        width: 32px;
        height: 32px;
        margin-right: 0.75rem;
    }
    
    .platform-icon-sm {
        font-size: 1rem;
    }
    
    #newReviewNotification {
        min-width: auto;
        left: 1rem;
        right: 1rem;
        transform: none;
    }
} 