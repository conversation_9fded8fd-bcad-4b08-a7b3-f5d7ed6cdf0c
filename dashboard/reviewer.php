<?php
require_once '../includes/auth.php';
require_login();
$user = current_user();
if ($user['role'] !== 'reviewer') {
    header('Location: /index.php');
    exit;
}

// Check if profile is completed
if (!$user['profile_completed']) {
    header('Location: reviewer_profile_setup.php');
    exit;
}

require_once '../includes/db.php';

// Flash message helper
function set_flash($msg) { $_SESSION['flash'] = $msg; }
function get_flash() { $msg = $_SESSION['flash'] ?? ''; unset($_SESSION['flash']); return $msg; }

// Handle review claim
if (isset($_POST['claim_review'])) {
    $review_id = intval($_POST['review_id']);
    
    // Check if reviewer already claimed a review from this customer today
    $stmt = $pdo->prepare('SELECT COUNT(*) as count FROM reviews WHERE reviewer_id = ? AND customer_id = (SELECT customer_id FROM reviews WHERE id = ?) AND DATE(created_at) = CURDATE()');
    $stmt->execute([$user['id'], $review_id]);
    $claimed_today = $stmt->fetch()['count'];
    
    if ($claimed_today >= 10) {
        set_flash('<div class="alert alert-warning">You have reached the daily limit of 10 reviews per customer. Please use a different Google account or try again tomorrow.</div>');
    } else {
        $pdo->beginTransaction();
        try {
            // Check if review is still available
            $stmt = $pdo->prepare('SELECT * FROM reviews WHERE id = ? AND status = "open" AND reviewer_id IS NULL');
    $stmt->execute([$review_id]);
    $review = $stmt->fetch();
            
    if ($review) {
                // Claim the review
                $stmt = $pdo->prepare('UPDATE reviews SET reviewer_id = ?, status = "claimed" WHERE id = ?');
                $stmt->execute([$user['id'], $review_id]);
                
                $pdo->commit();
                set_flash('<div class="alert alert-success">Review claimed successfully! You have 30 minutes to complete it.</div>');
            } else {
                set_flash('<div class="alert alert-danger">Review is no longer available.</div>');
            }
        } catch (Exception $e) {
            $pdo->rollBack();
            set_flash('<div class="alert alert-danger">Error claiming review. Please try again.</div>');
        }
    }
    header('Location: reviewer.php');
    exit;
}

// Handle review submission
if (isset($_POST['submit_review'])) {
    $review_id = intval($_POST['review_id']);
    $screenshot = $_FILES['screenshot'] ?? null;
    
    if ($screenshot && $screenshot['error'] === UPLOAD_ERR_OK) {
        // Validate file type
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!in_array($screenshot['type'], $allowed_types)) {
            set_flash('<div class="alert alert-danger">Only JPEG, PNG, and WebP images are allowed.</div>');
            header('Location: reviewer.php');
            exit;
        }
        
        // Validate file size (max 5MB)
        if ($screenshot['size'] > 5 * 1024 * 1024) {
            set_flash('<div class="alert alert-danger">Image size must be less than 5MB.</div>');
            header('Location: reviewer.php');
            exit;
        }
        
        // Create upload directory
        $upload_dir = __DIR__ . '/../uploads/reviews/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        
        // Generate unique filename
        $extension = pathinfo($screenshot['name'], PATHINFO_EXTENSION);
        $filename = 'review_' . $review_id . '_' . uniqid() . '.webp';
        $filepath = $upload_dir . $filename;
        
        // Compress and convert to WebP
        $image_data = file_get_contents($screenshot['tmp_name']);
        $image = imagecreatefromstring($image_data);
        
        if ($image !== false) {
            // Get original dimensions
            $width = imagesx($image);
            $height = imagesy($image);
            
            // Calculate new dimensions (max 1200px width/height)
            $max_dimension = 1200;
            if ($width > $max_dimension || $height > $max_dimension) {
                if ($width > $height) {
                    $new_width = $max_dimension;
                    $new_height = intval($height * $max_dimension / $width);
                } else {
                    $new_height = $max_dimension;
                    $new_width = intval($width * $max_dimension / $height);
                }
            } else {
                $new_width = $width;
                $new_height = $height;
            }
            
            // Create new image with new dimensions
            $new_image = imagecreatetruecolor($new_width, $new_height);
            
            // Preserve transparency for PNG
            if ($screenshot['type'] === 'image/png') {
                imagealphablending($new_image, false);
                imagesavealpha($new_image, true);
                $transparent = imagecolorallocatealpha($new_image, 255, 255, 255, 127);
                imagefilledrectangle($new_image, 0, 0, $new_width, $new_height, $transparent);
            }
            
            // Resize image
            imagecopyresampled($new_image, $image, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
            
            // Save as WebP with compression
            $quality = 85; // Good balance between quality and size
            if (imagewebp($new_image, $filepath, $quality)) {
                // Clean up memory
                imagedestroy($image);
                imagedestroy($new_image);
                
                // Update review status
                $pdo->beginTransaction();
                try {
                    $stmt = $pdo->prepare('UPDATE reviews SET status = "submitted", screenshot_url = ? WHERE id = ? AND reviewer_id = ?');
                    $stmt->execute(['/uploads/reviews/' . $filename, $review_id, $user['id']]);
                    
                    $pdo->commit();
                    set_flash('<div class="alert alert-success">Review submitted successfully! Admin will review and approve it.</div>');
                } catch (Exception $e) {
                    $pdo->rollBack();
                    // Delete uploaded file if database update fails
                    if (file_exists($filepath)) {
                        unlink($filepath);
                    }
                    set_flash('<div class="alert alert-danger">Error submitting review. Please try again.</div>');
                }
            } else {
                imagedestroy($image);
                imagedestroy($new_image);
                set_flash('<div class="alert alert-danger">Error processing image. Please try again.</div>');
            }
        } else {
            set_flash('<div class="alert alert-danger">Invalid image file. Please try again.</div>');
        }
    } else {
        set_flash('<div class="alert alert-danger">Please select a screenshot to submit.</div>');
    }
    
    header('Location: reviewer.php');
    exit;
}

// Fetch available reviews (only 1 per customer, excluding claimed ones)
$stmt = $pdo->prepare('
    SELECT r.*, u.name as customer_name 
    FROM reviews r 
    JOIN users u ON r.customer_id = u.id 
    WHERE r.status = "open" AND r.reviewer_id IS NULL 
    AND r.id IN (
        SELECT MIN(id) 
        FROM reviews 
        WHERE status = "open" AND reviewer_id IS NULL 
        GROUP BY customer_id
    )
    ORDER BY r.created_at DESC
');
$stmt->execute();
$available_reviews = $stmt->fetchAll();

// Fetch reviewer's claimed reviews with timer info
$stmt = $pdo->prepare('
    SELECT r.*, u.name as customer_name,
           TIMESTAMPDIFF(MINUTE, r.created_at, NOW()) as minutes_elapsed,
           CASE 
               WHEN r.status = "claimed" AND TIMESTAMPDIFF(MINUTE, r.created_at, NOW()) > 30 THEN "expired"
               WHEN r.status = "claimed" THEN "active"
               ELSE r.status
           END as timer_status
    FROM reviews r 
    JOIN users u ON r.customer_id = u.id 
    WHERE r.reviewer_id = ? AND r.status IN ("claimed", "submitted")
    ORDER BY r.created_at ASC
');
$stmt->execute([$user['id']]);
$claimed_reviews = $stmt->fetchAll();

// Fetch reviewer's completed reviews
$stmt = $pdo->prepare('
    SELECT r.*, u.name as customer_name 
    FROM reviews r 
    JOIN users u ON r.customer_id = u.id 
    WHERE r.reviewer_id = ? AND r.status IN ("completed", "approved")
    ORDER BY r.created_at DESC
    LIMIT 10
');
$stmt->execute([$user['id']]);
$completed_reviews = $stmt->fetchAll();

// Calculate total earnings (80% of review amounts)
$stmt = $pdo->prepare('
    SELECT SUM(amount * 0.8) as total_earnings 
    FROM reviews 
    WHERE reviewer_id = ? AND status IN ("completed", "approved")
');
$stmt->execute([$user['id']]);
$total_earnings = $stmt->fetch()['total_earnings'] ?? 0;

$message = get_flash();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reviewer Dashboard - 1xreviews</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --bg-light: #f8fafc;
            --border-color: #e5e7eb;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-light);
            line-height: 1.6;
        }
        
        .navbar {
            background: white !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }
        
        .main-content {
            padding-top: 80px;
        }
        
        .card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .card-header {
            background: white;
            border-bottom: 1px solid var(--border-color);
            border-radius: 16px 16px 0 0 !important;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .btn-primary {
            background: var(--primary-color);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }
        
        .btn-success {
            background: var(--success-color);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-success:hover {
            background: #059669;
            transform: translateY(-2px);
        }
        
        .btn-warning {
            background: var(--warning-color);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-warning:hover {
            background: #d97706;
            transform: translateY(-2px);
        }
        
        .wallet-card {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .wallet-amount {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }
        
        .review-card {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: white;
            transition: all 0.3s ease;
        }
        
        .review-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .platform-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .platform-google { background: #4285f4; color: white; }
        .platform-yelp { background: #ff1a1a; color: white; }
        .platform-facebook { background: #1877f2; color: white; }
        .platform-tripadvisor { background: #00aa6c; color: white; }
        .platform-trustpilot { background: #00b67a; color: white; }
        .platform-other { background: #6b7280; color: white; }
        
        .amount-badge {
            background: var(--success-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .earnings-badge {
            background: var(--accent-color);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-open { background: #fef3c7; color: #92400e; }
        .status-claimed { background: #dbeafe; color: #1e40af; }
        .status-submitted { background: #d1fae5; color: #065f46; }
        .status-completed { background: #dcfce7; color: #166534; }
        .status-approved { background: #bbf7d0; color: #15803d; }
        .status-rejected { background: #fecaca; color: #991b1b; }
        
        .status-badge.status-expired {
            background: var(--danger-color);
            color: white;
        }
        
        .timer-badge {
            background: var(--warning-color);
            color: white;
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .timer-display {
            background: var(--warning-color);
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 1.1rem;
        }
        
        .review-info-card {
            background: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            height: 100%;
        }
        
        .verification-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            height: 100%;
        }
        
        .review-text-box {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            max-height: 150px;
            overflow-y: auto;
            font-style: italic;
            color: var(--text-dark);
        }
        
        .screenshot-upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .screenshot-upload-area:hover {
            border-color: var(--primary-color);
            background: var(--bg-light);
        }
        
        .screenshot-preview {
            max-width: 100%;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 1rem;
        }
        
        .review-content {
            background: #f8fafc;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            position: relative;
        }
        
        .copy-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .copy-btn:hover {
            background: var(--secondary-color);
        }
        
        .modal-content {
            border-radius: 16px;
            border: none;
        }
        
        .modal-header {
            border-bottom: 1px solid var(--border-color);
            border-radius: 16px 16px 0 0;
        }
        
        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .screenshot-upload {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: white;
            cursor: pointer;
        }
        
        .screenshot-upload:hover {
            border-color: var(--primary-color);
            background: #eff6ff;
        }
        
        .refresh-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }
        
        .refresh-btn:hover {
            background: var(--secondary-color);
            transform: scale(1.1);
        }
        
        @media (max-width: 768px) {
            .wallet-amount {
                font-size: 2rem;
            }
            
            .refresh-btn {
                bottom: 1rem;
                right: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm fixed-top py-2">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center fw-bold" href="/dashboard/reviewer.php">
                <i class="fas fa-star text-primary me-2"></i>1xreviews
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar" aria-controls="mainNavbar" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="mainNavbar">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item"><a class="nav-link active" href="/dashboard/reviewer.php"><i class="fas fa-tachometer-alt me-1"></i>Dashboard</a></li>
                    <li class="nav-item"><a class="nav-link" href="/dashboard/request_withdrawal.php"><i class="fas fa-wallet me-1"></i>Earnings</a></li>
                    <li class="nav-item"><a class="nav-link" href="#leaderboard"><i class="fas fa-trophy me-1"></i>Leaderboard</a></li>
                    <li class="nav-item"><a class="nav-link" href="#support"><i class="fas fa-headset me-1"></i>Support</a></li>
                </ul>
                <div class="d-flex align-items-center gap-3">
                    <a href="#" class="btn btn-link position-relative p-0" title="Notifications">
                        <i class="fas fa-bell fa-lg"></i>
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style="font-size:0.7rem;">3</span>
                    </a>
                    <div class="dropdown">
                        <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <span class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width:32px;height:32px;font-weight:600;font-size:1rem;">
                                <?php echo strtoupper(substr($user['name'],0,1)); ?>
                            </span>
                            <span class="d-none d-lg-inline fw-semibold text-dark"><?php echo htmlspecialchars($user['name']); ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="my_account.php"><i class="fas fa-user me-2"></i>My Account</a></li>
                            <li><a class="dropdown-item" href="#settings"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="/logout.php"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="main-content">
<div class="container py-4">
            <?php echo $message; ?>
            
            <!-- Wallet Card -->
            <div class="wallet-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-2">Total Earnings</h4>
                        <div class="wallet-amount">$<?php echo number_format($total_earnings, 2); ?></div>
                        <p class="mb-0">You earn 80% of each review amount</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex flex-column align-items-end">
                            <a href="request_withdrawal.php" class="btn btn-light btn-lg mb-2">
                                <i class="fas fa-money-bill-wave me-2"></i>Request Withdrawal
                            </a>
                            <span class="badge bg-light text-dark mb-2">Available Reviews: <?php echo count($available_reviews); ?></span>
                            <span class="badge bg-light text-dark">Claimed Reviews: <?php echo count($claimed_reviews); ?></span>
                        </div>
                    </div>
    </div>
    </div>

    <div class="row">
                <!-- Available Reviews -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-list me-2"></i>Available Reviews</h5>
                            <button class="btn btn-sm btn-outline-primary" onclick="location.reload()">
                                <i class="fas fa-sync-alt me-2"></i>Refresh
                            </button>
                        </div>
                        <div class="card-body">
                            <?php if (empty($available_reviews)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                    <h5>No reviews available</h5>
                                    <p class="text-muted">Check back later for new review requests</p>
                                </div>
                            <?php else: ?>
                                <?php foreach ($available_reviews as $review): ?>
                                    <div class="review-card">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div class="platform-badge platform-<?php echo strtolower($review['platform']); ?>">
                                                <i class="fab fa-<?php echo strtolower($review['platform']); ?> me-2"></i>
                                                <?php echo htmlspecialchars($review['platform']); ?>
                                            </div>
                                            <div class="amount-badge">
                                                $<?php echo number_format($review['amount'], 2); ?>
                                            </div>
                                        </div>
                                        
                                        <h6 class="mb-2"><?php echo htmlspecialchars($review['business_name']); ?></h6>
                                        <p class="text-muted mb-3">
                                            <i class="fas fa-user me-2"></i>Posted by: <?php echo htmlspecialchars($review['customer_name']); ?>
                                            <?php if (isset($review['country']) && $review['country']): ?>
                                                <span class="ms-3"><i class="fas fa-flag me-2"></i><?php echo htmlspecialchars($review['country']); ?></span>
                                            <?php endif; ?>
                                        </p>
                                        
                                        <div class="review-content">
                                            <button class="copy-btn" onclick="copyContent('<?php echo addslashes($review['review_text']); ?>')">
                                                <i class="fas fa-copy me-1"></i>Copy
                                            </button>
                                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($review['review_text'])); ?></p>
                                        </div>
                                        
                                        <div class="d-flex gap-2 mt-3">
                                            <?php if (isset($review['business_link']) && $review['business_link']): ?>
                                                <a href="<?php echo htmlspecialchars($review['business_link']); ?>" target="_blank" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-external-link-alt me-2"></i>Open Rating Page
                                                </a>
                                            <?php endif; ?>
                                            <button class="btn btn-success btn-sm" onclick="claimReview(<?php echo $review['id']; ?>)">
                                                <i class="fas fa-hand-paper me-2"></i>Claim Review
                                            </button>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Claimed Reviews -->
                    <?php if (!empty($claimed_reviews)): ?>
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>My Claimed Reviews</h5>
                            </div>
                            <div class="card-body">
                                <?php foreach ($claimed_reviews as $review): ?>
                                    <div class="review-card">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <div class="platform-badge platform-<?php echo strtolower($review['platform']); ?>">
                                                <i class="fab fa-<?php echo strtolower($review['platform']); ?> me-2"></i>
                                                <?php echo htmlspecialchars($review['platform']); ?>
                                            </div>
                                            <div class="d-flex align-items-center gap-2">
                                                <div class="earnings-badge">
                                                    Earn: $<?php echo number_format($review['amount'] * 0.8, 2); ?>
                                                </div>
                                                <span class="status-badge status-<?php echo $review['timer_status']; ?>">
                                                    <?php echo ucfirst($review['timer_status']); ?>
                                                </span>
                                                <?php if ($review['timer_status'] === 'active'): ?>
                                                    <div class="timer-badge" data-claimed-at="<?php echo $review['created_at']; ?>">
                                                        <i class="fas fa-clock me-1"></i>
                                                        <span class="timer-text">30:00</span>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        
                                        <h6 class="mb-2"><?php echo htmlspecialchars($review['business_name']); ?></h6>
                                        <p class="text-muted mb-3">
                                            <i class="fas fa-user me-2"></i>Posted by: <?php echo htmlspecialchars($review['customer_name']); ?>
                                        </p>
                                        
                                        <div class="review-content">
                                            <button class="copy-btn" onclick="copyContent('<?php echo addslashes($review['review_text']); ?>')">
                                                <i class="fas fa-copy me-1"></i>Copy
                                            </button>
                                            <p class="mb-0"><?php echo nl2br(htmlspecialchars($review['review_text'])); ?></p>
                                        </div>
                                        
                                        <div class="d-flex gap-2 mt-3">
                                            <?php if (isset($review['business_link']) && $review['business_link']): ?>
                                                <a href="<?php echo htmlspecialchars($review['business_link']); ?>" target="_blank" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-external-link-alt me-2"></i>Open Rating Page
                                                </a>
                                            <?php endif; ?>
                                            <?php if ($review['timer_status'] === 'active'): ?>
                                                <button class="btn btn-warning btn-sm" onclick="openReviewPopup(<?php echo $review['id']; ?>)">
                                                    <i class="fas fa-play me-2"></i>Start Review
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Recent Earnings -->
            <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Recent Earnings</h5>
                        </div>
                <div class="card-body p-0">
                            <?php if (empty($completed_reviews)): ?>
                                <div class="p-4 text-center text-muted">
                                    <i class="fas fa-coins fa-2x mb-3"></i>
                                    <h6>No earnings yet</h6>
                                    <p class="small">Complete reviews to start earning</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                                <th>Business</th>
                                <th>Platform</th>
                                                <th>Earned</th>
                            </tr>
                        </thead>
                        <tbody>
                                            <?php foreach ($completed_reviews as $review): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($review['business_name']); ?></td>
                                                    <td>
                                                        <i class="fab fa-<?php echo strtolower($review['platform']); ?> me-2"></i>
                                                        <?php echo htmlspecialchars($review['platform']); ?>
                                                    </td>
                                                    <td class="text-success fw-bold">
                                                        $<?php echo number_format($review['amount'] * 0.8, 2); ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Daily Limit Info -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Daily Limits</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>Important</h6>
                                <ul class="mb-0 small">
                                    <li>Maximum 10 reviews per customer per day</li>
                                    <li>You earn 80% of the review amount</li>
                                    <li>Admin takes 20% as platform fee</li>
                                    <li>Use different Google accounts for more reviews</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Submit Review Modal -->
    <div class="modal fade" id="submitReviewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Submit Review</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="post" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="review_id" id="submitReviewId">
                        
                        <div class="mb-3">
                            <label class="form-label fw-bold">Review Screenshot *</label>
                            <div class="screenshot-upload" onclick="document.getElementById('reviewScreenshot').click()">
                                <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-3"></i>
                                <h5>Upload Screenshot</h5>
                                <p class="text-muted">Upload a screenshot of your posted review</p>
                                <p class="text-muted small">PNG, JPG up to 5MB</p>
                            </div>
                            <input type="file" name="review_screenshot" id="reviewScreenshot" class="d-none" accept="image/*" required>
                            <div id="reviewScreenshotPreview"></div>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-info-circle me-2"></i>
                            Please ensure your screenshot clearly shows the review posted on the platform. This will be verified by our admin team.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="submit_review" class="btn btn-success">Submit Review</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Review Popup Modal -->
    <div class="modal fade" id="reviewPopupModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Complete Review</h5>
                    <div class="d-flex align-items-center gap-3">
                        <div class="timer-display">
                            <i class="fas fa-clock text-warning me-2"></i>
                            <span id="popupTimer" class="fw-bold">30:00</span>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="review-info-card">
                                <h6 class="mb-3">Review Details</h6>
                                <div class="mb-3">
                                    <strong>Platform:</strong> <span id="popupPlatform"></span>
                                </div>
                                <div class="mb-3">
                                    <strong>Business:</strong> <span id="popupBusiness"></span>
                                </div>
                                <div class="mb-3">
                                    <strong>Review Text:</strong>
                                    <div class="review-text-box" id="popupReviewText"></div>
                                </div>
                                <div class="mb-3">
                                    <strong>Amount:</strong> $<span id="popupAmount"></span>
                                </div>
                                <div class="mb-3">
                                    <button class="btn btn-outline-primary btn-sm" onclick="copyReviewText()">
                                        <i class="fas fa-copy me-2"></i>Copy Review Text
                                    </button>
                                    <a href="#" id="popupBusinessLink" target="_blank" class="btn btn-outline-success btn-sm ms-2">
                                        <i class="fas fa-external-link-alt me-2"></i>Open Business Page
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="verification-card">
                                <h6 class="mb-3">Review Verification</h6>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Upload Review Screenshot *</label>
                                    <div class="screenshot-upload-area" onclick="document.getElementById('popupScreenshot').click()">
                                        <i class="fas fa-cloud-upload-alt fa-2x text-muted mb-3"></i>
                                        <h6>Click to Upload</h6>
                                        <p class="text-muted small">PNG, JPG up to 5MB</p>
                                    </div>
                                    <input type="file" id="popupScreenshot" class="d-none" accept="image/*">
                                    <div id="popupScreenshotPreview"></div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-bold">Review URL (Optional)</label>
                                    <input type="url" id="popupReviewUrl" class="form-control" placeholder="https://...">
                                </div>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Verification Steps:</strong>
                                    <ol class="mb-0 mt-2">
                                        <li>Copy the review text</li>
                                        <li>Go to the business page</li>
                                        <li>Post the review</li>
                                        <li>Take a screenshot</li>
                                        <li>Upload it here</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-success" onclick="submitPopupReview()">
                        <i class="fas fa-check me-2"></i>Submit Review
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="refresh-btn" onclick="location.reload()" title="Refresh Available Reviews">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentReviewId = null;
        let timerInterval = null;
        
        // Copy review content
        function copyContent(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show success message
                const btn = event.target.closest('.copy-btn');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
                btn.style.background = 'var(--success-color)';
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.style.background = 'var(--primary-color)';
                }, 2000);
            });
        }
        
        // Claim review function
        function claimReview(reviewId) {
            if (confirm('Are you sure you want to claim this review? You will have 30 minutes to complete it.')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `<input type="hidden" name="claim_review" value="1">
                                 <input type="hidden" name="review_id" value="${reviewId}">`;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // Submit review function (old method)
        function submitReview(reviewId) {
            document.getElementById('submitReviewId').value = reviewId;
            new bootstrap.Modal(document.getElementById('submitReviewModal')).show();
        }
        
        // Open review popup
        function openReviewPopup(reviewId) {
            currentReviewId = reviewId;
            
            // Fetch review details
            const url = `/dashboard/get_review_details.php?id=${reviewId}`;
            
            fetch(url)
                .then(res => res.json())
                .then(data => {
                    if (data.success) {
                        const review = data.review;
                        
                        // Populate popup
                        document.getElementById('popupPlatform').textContent = review.platform;
                        document.getElementById('popupBusiness').textContent = review.business_name;
                        document.getElementById('popupReviewText').textContent = review.review_text;
                        document.getElementById('popupAmount').textContent = (review.amount * 0.8).toFixed(2);
                        
                        if (review.business_link) {
                            document.getElementById('popupBusinessLink').href = review.business_link;
                            document.getElementById('popupBusinessLink').style.display = 'inline-block';
                        } else {
                            document.getElementById('popupBusinessLink').style.display = 'none';
                        }
                        
                        // Start timer
                        startTimer(review.created_at);
                        
                        // Show popup
                        new bootstrap.Modal(document.getElementById('reviewPopupModal')).show();
                    } else {
                        alert('Error loading review details: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Fetch error:', error);
                    alert('Error loading review details. Please check the console for details.');
                });
        }
        
        // Start timer
        function startTimer(claimedAt) {
            const claimedTime = new Date(claimedAt).getTime();
            const timeLimit = 30 * 60 * 1000; // 30 minutes in milliseconds
            
            function updateTimer() {
                const now = new Date().getTime();
                const elapsed = now - claimedTime;
                const remaining = timeLimit - elapsed;
                
                if (remaining <= 0) {
                    // Time expired
                    document.getElementById('popupTimer').textContent = '00:00';
                    document.getElementById('popupTimer').style.color = 'red';
                    clearInterval(timerInterval);
                    
                    // Auto-submit or show expired message
                    alert('Time expired! The review has been automatically released.');
                    location.reload();
                    return;
                }
                
                const minutes = Math.floor(remaining / 60000);
                const seconds = Math.floor((remaining % 60000) / 1000);
                const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                document.getElementById('popupTimer').textContent = timeString;
                
                // Change color when less than 5 minutes remaining
                if (remaining < 5 * 60 * 1000) {
                    document.getElementById('popupTimer').style.color = 'red';
                }
            }
            
            updateTimer();
            timerInterval = setInterval(updateTimer, 1000);
        }
        
        // Copy review text from popup
        function copyReviewText() {
            const text = document.getElementById('popupReviewText').textContent;
            navigator.clipboard.writeText(text).then(function() {
                alert('Review text copied to clipboard!');
            });
        }
        
        // Submit popup review
        function submitPopupReview() {
            const screenshot = document.getElementById('popupScreenshot').files[0];
            const reviewUrl = document.getElementById('popupReviewUrl').value;
            
            if (!screenshot) {
                alert('Please upload a screenshot of your review');
                return;
            }
            
            const formData = new FormData();
            formData.append('review_id', currentReviewId);
            formData.append('review_screenshot', screenshot);
            formData.append('review_url', reviewUrl);
            formData.append('submit_review', '1');
            
            fetch('/dashboard/submit_review_ajax.php', {
                method: 'POST',
                body: formData
            })
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    alert('Review submitted successfully!');
                    location.reload();
                } else {
                    alert('Error submitting review: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error submitting review');
            });
        }
        
        // Handle screenshot upload in popup
        document.getElementById('popupScreenshot').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('popupScreenshotPreview');
                    preview.innerHTML = `<img src="${e.target.result}" class="screenshot-preview">`;
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Update timers on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Update all timer badges
            document.querySelectorAll('.timer-badge').forEach(badge => {
                const claimedAt = badge.dataset.claimedAt;
                if (claimedAt) {
                    const claimedTime = new Date(claimedAt).getTime();
                    const now = new Date().getTime();
                    const elapsed = now - claimedTime;
                    const remaining = 30 * 60 * 1000 - elapsed; // 30 minutes
                    
                    if (remaining > 0) {
                        const minutes = Math.floor(remaining / 60000);
                        const seconds = Math.floor((remaining % 60000) / 1000);
                        const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                        badge.querySelector('.timer-text').textContent = timeString;
                        
                        // Auto-refresh page when time expires
                        setTimeout(() => {
                            location.reload();
                        }, remaining);
                    } else {
                        badge.querySelector('.timer-text').textContent = '00:00';
                        badge.style.background = 'var(--danger-color)';
                    }
                }
            });
        });
        
        // Handle screenshot upload
        document.getElementById('reviewScreenshot').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('reviewScreenshotPreview');
                    preview.innerHTML = `<img src="${e.target.result}" class="screenshot-preview">`;
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>
</html>
