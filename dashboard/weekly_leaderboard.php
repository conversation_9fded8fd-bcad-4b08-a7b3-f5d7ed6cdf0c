<!-- Weekly Leaderboard -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Weekly Leaderboard</h5>
        <small class="text-muted">Top reviewers this week</small>
    </div>
    <div class="card-body p-0">
        <?php if (empty($weekly_leaderboard)): ?>
            <div class="p-4 text-center text-muted">
                <i class="fas fa-trophy fa-2x mb-3 opacity-50"></i>
                <h6>No data yet</h6>
                <p class="small">Complete reviews to appear on the leaderboard</p>
            </div>
        <?php else: ?>
            <div class="leaderboard-list">
                <?php foreach ($weekly_leaderboard as $index => $leader): ?>
                    <div class="leaderboard-item <?php echo $index < 3 ? 'top-performer' : ''; ?>">
                        <div class="rank-badge rank-<?php echo $index + 1; ?>">
                            <?php if ($index === 0): ?>
                                <i class="fas fa-crown"></i>
                            <?php elseif ($index === 1): ?>
                                <i class="fas fa-medal"></i>
                            <?php elseif ($index === 2): ?>
                                <i class="fas fa-award"></i>
                            <?php else: ?>
                                <?php echo $index + 1; ?>
                            <?php endif; ?>
                        </div>
                        <div class="leader-info">
                            <div class="leader-name">
                                <?php echo htmlspecialchars($leader['name']); ?>
                                <?php if ($leader['name'] === $user['name']): ?>
                                    <span class="badge bg-primary ms-1">You</span>
                                <?php endif; ?>
                            </div>
                            <div class="leader-stats">
                                <span class="reviews-count"><?php echo $leader['reviews_completed']; ?> reviews</span>
                                <span class="earnings-amount">$<?php echo number_format($leader['total_earned'], 2); ?></span>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="card-footer text-center bg-light">
                <small class="text-muted">
                    <i class="fas fa-calendar me-1"></i>Resets every Monday
                </small>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Recent Earnings (Enhanced) -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Recent Earnings</h5>
    </div>
    <div class="card-body p-0">
        <?php if (empty($completed_reviews)): ?>
            <div class="p-4 text-center text-muted">
                <i class="fas fa-coins fa-2x mb-3"></i>
                <h6>No earnings yet</h6>
                <p class="small">Complete reviews to start earning</p>
            </div>
        <?php else: ?>
            <div class="earnings-list">
                <?php foreach ($completed_reviews as $review): ?>
                    <div class="earning-item">
                        <div class="earning-platform">
                            <i class="fab fa-<?php echo strtolower($review['platform']); ?> platform-icon-sm"></i>
                        </div>
                        <div class="earning-details">
                            <div class="earning-business"><?php echo htmlspecialchars($review['business_name']); ?></div>
                            <div class="earning-date"><?php echo date('M j, g:i A', strtotime($review['completed_at'] ?? $review['created_at'])); ?></div>
                        </div>
                        <div class="earning-amount">
                            $<?php echo number_format($review['amount'] * 0.8, 2); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div> 