<?php
require_once '../includes/auth.php';
require_login();
$user = current_user();
require_once '../includes/db.php';

// Flash message helper
function set_flash($msg) { $_SESSION['flash'] = $msg; }
function get_flash() { $msg = $_SESSION['flash'] ?? ''; unset($_SESSION['flash']); return $msg; }

// Handle profile updates
if (isset($_POST['update_profile'])) {
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $whatsapp = trim($_POST['whatsapp'] ?? '');
    $upi_id = trim($_POST['upi_id'] ?? '');
    $upi_qr = $user['upi_qr']; // Keep existing if not updated
    
    // Validate required fields
    $errors = [];
    if (empty($name)) $errors[] = 'Name is required';
    if (empty($email)) $errors[] = 'Email is required';
    
    // For reviewers, validate additional fields
    if ($user['role'] === 'reviewer') {
        if (empty($whatsapp)) $errors[] = 'WhatsApp number is required';
        if (empty($upi_id)) $errors[] = 'UPI ID is required';
    }
    
    // Handle UPI QR code upload if provided
    if (!empty($_FILES['upi_qr']['name'])) {
        $target_dir = __DIR__ . '/../uploads/upi_qr/';
        if (!is_dir($target_dir)) mkdir($target_dir, 0777, true);
        $filename = uniqid('upi_') . '_' . basename($_FILES['upi_qr']['name']);
        $target_file = $target_dir . $filename;
        if (move_uploaded_file($_FILES['upi_qr']['tmp_name'], $target_file)) {
            $upi_qr = '/uploads/upi_qr/' . $filename;
        } else {
            $errors[] = 'Error uploading UPI QR code';
        }
    }
    
    if (empty($errors)) {
        try {
            // Update user profile
            if ($user['role'] === 'reviewer') {
                $stmt = $pdo->prepare('UPDATE users SET name = ?, email = ?, whatsapp = ?, upi_id = ?, upi_qr = ? WHERE id = ?');
                $stmt->execute([$name, $email, $whatsapp, $upi_id, $upi_qr, $user['id']]);
            } else {
                $stmt = $pdo->prepare('UPDATE users SET name = ?, email = ? WHERE id = ?');
                $stmt->execute([$name, $email, $user['id']]);
            }
            
            set_flash('<div class="alert alert-success">Profile updated successfully!</div>');
            header('Location: my_account.php');
            exit;
        } catch (Exception $e) {
            set_flash('<div class="alert alert-danger">Error updating profile. Please try again.</div>');
        }
    } else {
        set_flash('<div class="alert alert-danger">' . implode(', ', $errors) . '</div>');
    }
}

// Handle role change
if (isset($_POST['change_role'])) {
    $new_role = trim($_POST['new_role'] ?? '');
    
    if (in_array($new_role, ['customer', 'reviewer'])) {
        try {
            // Update user role
            $stmt = $pdo->prepare('UPDATE users SET role = ? WHERE id = ?');
            $stmt->execute([$new_role, $user['id']]);
            
            // Update session
            $_SESSION['role'] = $new_role;
            
            set_flash('<div class="alert alert-success">Role changed successfully! You will be redirected to your new dashboard.</div>');
            
            // Redirect based on new role
            if ($new_role === 'reviewer' && !$user['profile_completed']) {
                header('Location: reviewer_profile_setup.php');
            } elseif ($new_role === 'reviewer') {
                header('Location: reviewer.php');
            } else {
                header('Location: customer.php');
            }
            exit;
        } catch (Exception $e) {
            set_flash('<div class="alert alert-danger">Error changing role. Please try again.</div>');
        }
    } else {
        set_flash('<div class="alert alert-danger">Invalid role selected.</div>');
    }
}

$message = get_flash();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Account - 1xreviews</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --bg-light: #f8fafc;
            --border-color: #e5e7eb;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-light);
            line-height: 1.6;
        }
        
        .navbar {
            background: white !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }
        
        .main-content {
            padding-top: 80px;
        }
        
        .card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .card-header {
            background: white;
            border-bottom: 1px solid var(--border-color);
            border-radius: 16px 16px 0 0 !important;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .btn-primary {
            background: var(--primary-color);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }
        
        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem 1.25rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .form-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }
        
        .required-field::after {
            content: " *";
            color: var(--danger-color);
        }
        
        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--bg-light);
        }
        
        .upload-area:hover {
            border-color: var(--primary-color);
            background: #eff6ff;
        }
        
        .preview-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 1rem;
        }
        
        .profile-section {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .role-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: capitalize;
        }
        
        .role-customer {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .role-reviewer {
            background: #dcfce7;
            color: #166534;
        }
        
        .role-admin {
            background: #fef3c7;
            color: #92400e;
        }
        
        .stats-card {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .nav-pills .nav-link {
            border-radius: 12px;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        .nav-pills .nav-link.active {
            background: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-star me-2"></i>1xreviews
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<?php echo $user['role'] === 'reviewer' ? 'reviewer.php' : 'customer.php'; ?>">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
                <a class="nav-link" href="/logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="main-content">
        <div class="container py-4">
            <?php echo $message; ?>
            
            <!-- Account Stats -->
            <div class="stats-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-2">Welcome, <?php echo htmlspecialchars($user['name']); ?>!</h4>
                        <p class="mb-0">Manage your account settings and profile information</p>
                    </div>
                    <div class="col-md-4 text-end">
                        <span class="role-badge role-<?php echo $user['role']; ?>">
                            <i class="fas fa-user me-2"></i><?php echo ucfirst($user['role']); ?>
                        </span>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Navigation -->
                <div class="col-lg-3">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-cog me-2"></i>Account Settings</h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist">
                                <button class="nav-link active" data-bs-toggle="pill" data-bs-target="#profile" type="button">
                                    <i class="fas fa-user me-2"></i>Profile
                                </button>
                                <button class="nav-link" data-bs-toggle="pill" data-bs-target="#role" type="button">
                                    <i class="fas fa-exchange-alt me-2"></i>Change Role
                                </button>
                                <button class="nav-link" data-bs-toggle="pill" data-bs-target="#security" type="button">
                                    <i class="fas fa-shield-alt me-2"></i>Security
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content -->
                <div class="col-lg-9">
                    <div class="tab-content" id="v-pills-tabContent">
                        <!-- Profile Tab -->
                        <div class="tab-pane fade show active" id="profile">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>Profile Information</h5>
                                </div>
                                <div class="card-body">
                                    <form method="post" enctype="multipart/form-data">
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label required-field">Full Name</label>
                                                <input type="text" name="name" class="form-control" value="<?php echo htmlspecialchars($user['name']); ?>" required>
                                            </div>
                                            
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label required-field">Email Address</label>
                                                <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                            </div>
                                        </div>
                                        
                                        <?php if ($user['role'] === 'reviewer'): ?>
                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label required-field">WhatsApp Number</label>
                                                <input type="tel" name="whatsapp" class="form-control" value="<?php echo htmlspecialchars($user['whatsapp'] ?? ''); ?>" placeholder="+91 98765 43210" required>
                                                <div class="form-text">Include country code for international numbers</div>
                                            </div>
                                            
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label required-field">UPI ID</label>
                                                <input type="text" name="upi_id" class="form-control" value="<?php echo htmlspecialchars($user['upi_id'] ?? ''); ?>" placeholder="username@upi" required>
                                                <div class="form-text">Your UPI ID for receiving payments</div>
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label class="form-label">UPI QR Code</label>
                                            <?php if ($user['upi_qr']): ?>
                                                <div class="mb-2">
                                                    <img src="<?php echo htmlspecialchars($user['upi_qr']); ?>" class="preview-image" alt="Current UPI QR">
                                                    <p class="text-muted small">Current QR code</p>
                                                </div>
                                            <?php endif; ?>
                                            <div class="upload-area" onclick="document.getElementById('upiQr').click()">
                                                <i class="fas fa-qrcode fa-2x text-muted mb-3"></i>
                                                <h6>Update UPI QR Code</h6>
                                                <p class="text-muted small">Click to upload or drag & drop</p>
                                                <p class="text-muted small">PNG, JPG up to 2MB</p>
                                            </div>
                                            <input type="file" name="upi_qr" id="upiQr" class="d-none" accept="image/*">
                                            <div id="upiQrPreview"></div>
                                        </div>
                                        <?php endif; ?>
                                        
                                        <div class="text-end">
                                            <button type="submit" name="update_profile" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>Update Profile
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Role Tab -->
                        <div class="tab-pane fade" id="role">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-exchange-alt me-2"></i>Change Role</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Current Role:</strong> <?php echo ucfirst($user['role']); ?>
                                    </div>
                                    
                                    <form method="post">
                                        <div class="mb-3">
                                            <label class="form-label">Select New Role</label>
                                            <select name="new_role" class="form-select" required>
                                                <option value="">Choose a role...</option>
                                                <option value="customer" <?php echo $user['role'] === 'customer' ? 'selected' : ''; ?>>Customer - Buy Reviews</option>
                                                <option value="reviewer" <?php echo $user['role'] === 'reviewer' ? 'selected' : ''; ?>>Reviewer - Work & Earn</option>
                                            </select>
                                        </div>
                                        
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>Important:</strong> Changing your role will redirect you to the appropriate dashboard. If you switch to reviewer and haven't completed your profile, you'll be asked to do so first.
                                        </div>
                                        
                                        <div class="text-end">
                                            <button type="submit" name="change_role" class="btn btn-warning" onclick="return confirm('Are you sure you want to change your role?')">
                                                <i class="fas fa-exchange-alt me-2"></i>Change Role
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Security Tab -->
                        <div class="tab-pane fade" id="security">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Security Settings</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Account Security:</strong> Your account is secured with Google OAuth. No password changes are needed.
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center p-3 border rounded">
                                                <i class="fas fa-google fa-2x text-primary me-3"></i>
                                                <div>
                                                    <h6 class="mb-1">Google OAuth</h6>
                                                    <p class="mb-0 text-muted">Connected via Google</p>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="d-flex align-items-center p-3 border rounded">
                                                <i class="fas fa-clock fa-2x text-success me-3"></i>
                                                <div>
                                                    <h6 class="mb-1">Last Login</h6>
                                                    <p class="mb-0 text-muted">Recently</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="mt-3">
                                        <a href="/logout.php" class="btn btn-outline-danger">
                                            <i class="fas fa-sign-out-alt me-2"></i>Logout from All Devices
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Handle UPI QR code upload
        document.getElementById('upiQr').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('upiQrPreview');
                    preview.innerHTML = `<img src="${e.target.result}" class="preview-image">`;
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Drag and drop functionality
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('upiQr').files = files;
                const event = new Event('change');
                document.getElementById('upiQr').dispatchEvent(event);
            }
        });
    </script>
</body>
</html> 