<?php
require_once '../includes/auth.php';
require_login();
$user = current_user();
if ($user['role'] !== 'reviewer') {
    header('Location: /index.php');
    exit;
}

// Check if profile is completed
if (!$user['profile_completed']) {
    header('Location: reviewer_profile_setup.php');
    exit;
}

require_once '../includes/db.php';

// Flash message helper
function set_flash($msg) { $_SESSION['flash'] = $msg; }
function get_flash() { $msg = $_SESSION['flash'] ?? ''; unset($_SESSION['flash']); return $msg; }

// Handle review claim with 15-minute timer
if (isset($_POST['claim_review'])) {
    $review_id = intval($_POST['review_id']);
    
    // REMOVED: Daily limit check - No longer restricting reviews per day
    
    $pdo->beginTransaction();
    try {
        // Check if review is still available
        $stmt = $pdo->prepare('SELECT * FROM reviews WHERE id = ? AND status = "open" AND reviewer_id IS NULL');
        $stmt->execute([$review_id]);
        $review = $stmt->fetch();
        
        if ($review) {
            // Claim the review with 15-minute timer
            $claimed_at = date('Y-m-d H:i:s');
            $stmt = $pdo->prepare('UPDATE reviews SET reviewer_id = ?, status = "claimed", claimed_at = ? WHERE id = ?');
            $stmt->execute([$user['id'], $claimed_at, $review_id]);
            
            $pdo->commit();
            set_flash('<div class="alert alert-success">Review claimed successfully! You have 15 minutes to complete it.</div>');
        } else {
            set_flash('<div class="alert alert-danger">Review is no longer available.</div>');
        }
    } catch (Exception $e) {
        $pdo->rollBack();
        set_flash('<div class="alert alert-danger">Error claiming review. Please try again.</div>');
    }
    header('Location: reviewer.php');
    exit;
}

// Handle "Mark as Completed" button
if (isset($_POST['mark_completed'])) {
    $review_id = intval($_POST['review_id']);
    
    $pdo->beginTransaction();
    try {
        // Mark review as completed
        $stmt = $pdo->prepare('UPDATE reviews SET status = "completed", completed_at = NOW() WHERE id = ? AND reviewer_id = ? AND status = "claimed"');
        $stmt->execute([$review_id, $user['id']]);
        
        if ($stmt->rowCount() > 0) {
            $pdo->commit();
            set_flash('<div class="alert alert-success">Review marked as completed! It has been moved to Recent Earnings.</div>');
        } else {
            $pdo->rollBack();
            set_flash('<div class="alert alert-danger">Unable to mark review as completed.</div>');
        }
    } catch (Exception $e) {
        $pdo->rollBack();
        set_flash('<div class="alert alert-danger">Error updating review status. Please try again.</div>');
    }
    header('Location: reviewer.php');
    exit;
}

// Handle proof submission (screenshot + link)
if (isset($_POST['submit_proof'])) {
    $review_id = intval($_POST['review_id']);
    $review_link = $_POST['review_link'] ?? '';
    $screenshot = $_FILES['proof_screenshot'] ?? null;
    
    $screenshot_path = null;
    
    // Handle screenshot upload
    if ($screenshot && $screenshot['error'] === UPLOAD_ERR_OK) {
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!in_array($screenshot['type'], $allowed_types)) {
            set_flash('<div class="alert alert-danger">Only JPEG, PNG, and WebP images are allowed.</div>');
            header('Location: reviewer.php');
            exit;
        }
        
        if ($screenshot['size'] > 5 * 1024 * 1024) {
            set_flash('<div class="alert alert-danger">Image size must be less than 5MB.</div>');
            header('Location: reviewer.php');
            exit;
        }
        
        $upload_dir = __DIR__ . '/../uploads/proofs/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        
        $extension = pathinfo($screenshot['name'], PATHINFO_EXTENSION);
        $filename = 'proof_' . $review_id . '_' . uniqid() . '.' . $extension;
        $filepath = $upload_dir . $filename;
        
        if (move_uploaded_file($screenshot['tmp_name'], $filepath)) {
            $screenshot_path = '/uploads/proofs/' . $filename;
        }
    }
    
    // Update review with proof
    $pdo->beginTransaction();
    try {
        $stmt = $pdo->prepare('UPDATE reviews SET proof_screenshot = ?, proof_link = ?, proof_submitted_at = NOW(), status = "proof_submitted" WHERE id = ? AND reviewer_id = ?');
        $stmt->execute([$screenshot_path, $review_link, $review_id, $user['id']]);
        
        $pdo->commit();
        set_flash('<div class="alert alert-success">Proof submitted successfully! Waiting for admin verification.</div>');
    } catch (Exception $e) {
        $pdo->rollBack();
        set_flash('<div class="alert alert-danger">Error submitting proof. Please try again.</div>');
    }
    header('Location: reviewer.php');
    exit;
}

// Auto-expire reviews after 15 minutes
$stmt = $pdo->prepare('
    UPDATE reviews 
    SET status = "expired", reviewer_id = NULL 
    WHERE status = "claimed" 
    AND claimed_at IS NOT NULL 
    AND TIMESTAMPDIFF(MINUTE, claimed_at, NOW()) >= 15
');
$stmt->execute();

// Fetch available reviews
$stmt = $pdo->prepare('
    SELECT r.*, u.name as customer_name 
    FROM reviews r 
    JOIN users u ON r.customer_id = u.id 
    WHERE r.status = "open" AND r.reviewer_id IS NULL 
    ORDER BY r.created_at DESC
');
$stmt->execute();
$available_reviews = $stmt->fetchAll();

// Fetch reviewer's claimed reviews with timer info
$stmt = $pdo->prepare('
    SELECT r.*, u.name as customer_name,
           TIMESTAMPDIFF(MINUTE, r.claimed_at, NOW()) as minutes_elapsed,
           CASE 
               WHEN r.status = "claimed" AND TIMESTAMPDIFF(MINUTE, r.claimed_at, NOW()) >= 15 THEN "expired"
               WHEN r.status = "claimed" THEN "active"
               ELSE r.status
           END as timer_status
    FROM reviews r 
    JOIN users u ON r.customer_id = u.id 
    WHERE r.reviewer_id = ? AND r.status IN ("claimed", "proof_submitted")
    ORDER BY r.claimed_at ASC
');
$stmt->execute([$user['id']]);
$claimed_reviews = $stmt->fetchAll();

// Fetch reviewer's completed reviews for Recent Earnings
$stmt = $pdo->prepare('
    SELECT r.*, u.name as customer_name 
    FROM reviews r 
    JOIN users u ON r.customer_id = u.id 
    WHERE r.reviewer_id = ? AND r.status IN ("completed", "approved")
    ORDER BY r.completed_at DESC, r.created_at DESC
    LIMIT 10
');
$stmt->execute([$user['id']]);
$completed_reviews = $stmt->fetchAll();

// Enhanced Wallet Summary Calculations
$stmt = $pdo->prepare('
    SELECT 
        SUM(CASE WHEN status IN ("completed", "approved") THEN amount * 0.8 ELSE 0 END) as total_earnings,
        SUM(CASE WHEN status = "proof_submitted" THEN amount * 0.8 ELSE 0 END) as pending_approval,
        SUM(CASE WHEN status = "approved" AND paid = 1 THEN amount * 0.8 ELSE 0 END) as paid,
        SUM(CASE WHEN status IN ("completed", "approved") AND (paid IS NULL OR paid = 0) THEN amount * 0.8 ELSE 0 END) as withdrawable
    FROM reviews 
    WHERE reviewer_id = ?
');
$stmt->execute([$user['id']]);
$wallet_data = $stmt->fetch();

$total_earnings = $wallet_data['total_earnings'] ?? 0;
$pending_approval = $wallet_data['pending_approval'] ?? 0;
$paid_amount = $wallet_data['paid'] ?? 0;
$withdrawable_balance = $wallet_data['withdrawable'] ?? 0;

// Weekly Leaderboard (Top 5 reviewers this week)
$stmt = $pdo->prepare('
    SELECT 
        u.name,
        COUNT(r.id) as reviews_completed,
        SUM(r.amount * 0.8) as total_earned
    FROM reviews r
    JOIN users u ON r.reviewer_id = u.id
    WHERE r.status IN ("completed", "approved")
    AND YEARWEEK(r.completed_at, 1) = YEARWEEK(CURDATE(), 1)
    GROUP BY r.reviewer_id, u.name
    ORDER BY reviews_completed DESC, total_earned DESC
    LIMIT 5
');
$stmt->execute();
$weekly_leaderboard = $stmt->fetchAll();

$message = get_flash();
?> 