// Enhanced Reviewer Dashboard JavaScript

// Global variables
let timerIntervals = new Map();
let lastReviewCount = 0;
let currentFilter = 'all';

// Initialize dashboard on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeTimers();
    initializeFilterTabs();
    initializeProofUpload();
    checkForNewReviews();
    
    // Auto-refresh every 30 seconds
    setInterval(checkForNewReviews, 30000);
});

// Initialize 15-minute countdown timers
function initializeTimers() {
    document.querySelectorAll('.timer-badge-15min').forEach(badge => {
        const claimedAt = badge.dataset.claimedAt;
        if (claimedAt) {
            startCountdownTimer(badge, claimedAt);
        }
    });
}

// Start individual countdown timer
function startCountdownTimer(badge, claimedAt) {
    const claimedTime = new Date(claimedAt).getTime();
    const timeLimit = 15 * 60 * 1000; // 15 minutes in milliseconds
    const timerText = badge.querySelector('.timer-text-15min');
    
    function updateTimer() {
        const now = new Date().getTime();
        const elapsed = now - claimedTime;
        const remaining = timeLimit - elapsed;
        
        if (remaining <= 0) {
            // Time expired
            timerText.textContent = '00:00';
            badge.classList.add('expired');
            badge.style.background = 'var(--danger-color)';
            
            // Mark parent review card as expired
            const reviewCard = badge.closest('.enhanced-review-card');
            if (reviewCard) {
                reviewCard.dataset.status = 'expired';
            }
            
            clearInterval(timerIntervals.get(badge));
            timerIntervals.delete(badge);
            
            // Show notification and auto-reload after 5 seconds
            showExpiredNotification();
            setTimeout(() => location.reload(), 5000);
            return;
        }
        
        const minutes = Math.floor(remaining / 60000);
        const seconds = Math.floor((remaining % 60000) / 1000);
        const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        timerText.textContent = timeString;
        
        // Change color when less than 5 minutes remaining
        if (remaining < 5 * 60 * 1000) {
            badge.style.background = 'var(--danger-color)';
            badge.classList.add('urgent');
        }
    }
    
    updateTimer();
    const interval = setInterval(updateTimer, 1000);
    timerIntervals.set(badge, interval);
}

// Show expired notification
function showExpiredNotification() {
    const notification = document.createElement('div');
    notification.className = 'alert alert-danger alert-dismissible fade show';
    notification.style.position = 'fixed';
    notification.style.top = '80px';
    notification.style.left = '50%';
    notification.style.transform = 'translateX(-50%)';
    notification.style.zIndex = '1060';
    notification.innerHTML = `
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>Time Expired!</strong> Your claimed review has been automatically released.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Initialize filter tabs
function initializeFilterTabs() {
    document.querySelectorAll('.filter-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            const filter = this.dataset.filter;
            setActiveFilter(filter);
            filterReviews(filter);
        });
    });
}

// Set active filter tab
function setActiveFilter(filter) {
    currentFilter = filter;
    document.querySelectorAll('.filter-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`.filter-tab[data-filter="${filter}"]`).classList.add('active');
}

// Filter reviews based on status
function filterReviews(filter) {
    document.querySelectorAll('.enhanced-review-card').forEach(card => {
        const status = card.dataset.status;
        let shouldShow = false;
        
        switch(filter) {
            case 'all':
                shouldShow = true;
                break;
            case 'pending':
                shouldShow = status === 'active' || status === 'proof_submitted';
                break;
            case 'completed':
                shouldShow = status === 'completed';
                break;
            case 'expired':
                shouldShow = status === 'expired';
                break;
        }
        
        if (shouldShow) {
            card.classList.remove('filtered-out');
            card.style.display = 'block';
        } else {
            card.classList.add('filtered-out');
            card.style.display = 'none';
        }
    });
}

// Mark review as completed
function markAsCompleted(reviewId) {
    if (confirm('Mark this review as completed? This will move it to Recent Earnings.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="mark_completed" value="1">
            <input type="hidden" name="review_id" value="${reviewId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Open proof submission modal
function openProofModal(reviewId) {
    document.getElementById('proofReviewId').value = reviewId;
    
    // Reset form
    document.getElementById('proofSubmissionForm').reset();
    document.getElementById('proofScreenshotPreview').innerHTML = '';
    
    new bootstrap.Modal(document.getElementById('submitProofModal')).show();
}

// Initialize proof upload functionality
function initializeProofUpload() {
    const proofScreenshot = document.getElementById('proofScreenshot');
    if (proofScreenshot) {
        proofScreenshot.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Validate file
                if (!file.type.startsWith('image/')) {
                    alert('Please select an image file');
                    this.value = '';
                    return;
                }
                
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB');
                    this.value = '';
                    return;
                }
                
                // Show preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('proofScreenshotPreview');
                    preview.innerHTML = `
                        <div class="uploaded-file-preview">
                            <img src="${e.target.result}" class="screenshot-preview" style="max-width: 100%; max-height: 200px; border-radius: 8px;">
                            <p class="mt-2 text-success">
                                <i class="fas fa-check me-2"></i>Screenshot ready for upload
                            </p>
                        </div>
                    `;
                };
                reader.readAsDataURL(file);
            }
        });
    }
}

// Check for new reviews (notification system)
function checkForNewReviews() {
    fetch('/dashboard/get_available_count.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const currentCount = data.count;
                
                // Show notification if new reviews are available
                if (lastReviewCount > 0 && currentCount > lastReviewCount) {
                    const newReviews = currentCount - lastReviewCount;
                    showNewReviewNotification(newReviews, data.latest_amount || 0);
                }
                
                lastReviewCount = currentCount;
                
                // Update available count badge
                const availableBadge = document.querySelector('.badge:contains("Available")');
                if (availableBadge) {
                    availableBadge.textContent = `Available: ${currentCount}`;
                }
            }
        })
        .catch(error => console.log('Error checking for new reviews:', error));
}

// Show new review notification
function showNewReviewNotification(count, amount) {
    const notification = document.getElementById('newReviewNotification');
    if (notification) {
        const amountSpan = document.getElementById('newReviewAmount');
        if (amountSpan) {
            amountSpan.textContent = amount.toFixed(2);
        }
        
        notification.classList.remove('d-none');
        notification.classList.add('show');
        
        // Auto-hide after 10 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.classList.add('d-none');
            }, 150);
        }, 10000);
    }
}

// Copy review content with enhanced feedback
function copyContent(text) {
    navigator.clipboard.writeText(text).then(function() {
        const btn = event.target.closest('.copy-btn');
        const originalText = btn.innerHTML;
        
        // Show success feedback
        btn.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
        btn.style.background = 'var(--success-color)';
        btn.style.transform = 'scale(1.1)';
        
        // Start 15-minute timer when content is copied (if from claimed review)
        const reviewCard = btn.closest('.enhanced-review-card');
        if (reviewCard && reviewCard.dataset.status === 'active') {
            const timerBadge = reviewCard.querySelector('.timer-badge-15min');
            if (timerBadge && !timerBadge.classList.contains('started')) {
                timerBadge.classList.add('started');
                // Timer is already running from initialization
            }
        }
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = 'var(--primary-color)';
            btn.style.transform = 'scale(1)';
        }, 2000);
    }).catch(function() {
        alert('Failed to copy text. Please try again.');
    });
}

// Claim review function with enhanced feedback
function claimReview(reviewId) {
    if (confirm('Claim this review? You will have 15 minutes to complete it.')) {
        // Show loading state
        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Claiming...';
        button.disabled = true;
        
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="claim_review" value="1">
            <input type="hidden" name="review_id" value="${reviewId}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Enhanced refresh function
function refreshAvailableReviews() {
    const refreshBtn = document.querySelector('.refresh-btn');
    if (refreshBtn) {
        refreshBtn.style.transform = 'scale(1.1) rotate(360deg)';
        setTimeout(() => {
            location.reload();
        }, 500);
    } else {
        location.reload();
    }
}

// Auto-save proof form data
function autoSaveProofData() {
    const form = document.getElementById('proofSubmissionForm');
    if (form) {
        form.addEventListener('input', function() {
            const formData = new FormData(form);
            const data = {};
            for (let [key, value] of formData.entries()) {
                if (key !== 'proof_screenshot') { // Don't save file data
                    data[key] = value;
                }
            }
            localStorage.setItem('proofFormData', JSON.stringify(data));
        });
        
        // Restore saved data
        const savedData = localStorage.getItem('proofFormData');
        if (savedData) {
            const data = JSON.parse(savedData);
            Object.keys(data).forEach(key => {
                const input = form.querySelector(`[name="${key}"]`);
                if (input && input.type !== 'file') {
                    input.value = data[key];
                }
            });
        }
    }
}

// Clear auto-saved data on successful submission
document.addEventListener('DOMContentLoaded', function() {
    autoSaveProofData();
    
    // Clear saved data if flash message indicates success
    const flashMessage = document.querySelector('.alert-success');
    if (flashMessage && flashMessage.textContent.includes('Proof submitted')) {
        localStorage.removeItem('proofFormData');
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + R for refresh
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        refreshAvailableReviews();
    }
    
    // Escape to close modals
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            bootstrap.Modal.getInstance(modal)?.hide();
        });
    }
});

// Performance optimization: Lazy load review content
function optimizeReviewLoading() {
    const reviewCards = document.querySelectorAll('.enhanced-review-card');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('loaded');
                observer.unobserve(entry.target);
            }
        });
    }, {
        rootMargin: '50px'
    });
    
    reviewCards.forEach(card => {
        observer.observe(card);
    });
}

// Initialize performance optimizations
document.addEventListener('DOMContentLoaded', optimizeReviewLoading); 