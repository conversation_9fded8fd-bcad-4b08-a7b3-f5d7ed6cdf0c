<?php
require_once '../includes/auth.php';
require_login();
$user = current_user();
require_once '../includes/db.php';
require_once '../includes/wallet.php';

// Flash message helper
function set_flash($msg) { $_SESSION['flash'] = $msg; }
function get_flash() { $msg = $_SESSION['flash'] ?? ''; unset($_SESSION['flash']); return $msg; }

// Get currency settings
$currency_settings = get_currency_settings();

// Calculate available balance (80% of approved reviews)
$stmt = $pdo->prepare('
    SELECT SUM(amount * 0.8) as total_earnings 
    FROM reviews 
    WHERE reviewer_id = ? AND status = "approved"
');
$stmt->execute([$user['id']]);
$total_earnings = $stmt->fetch()['total_earnings'] ?? 0;

// Calculate already withdrawn amount
$stmt = $pdo->prepare('
    SELECT SUM(amount) as total_withdrawn 
    FROM withdrawals 
    WHERE user_id = ? AND status = "approved"
');
$stmt->execute([$user['id']]);
$total_withdrawn = $stmt->fetch()['total_withdrawn'] ?? 0;

$available_balance = $total_earnings - $total_withdrawn;

// Handle withdrawal request
if (isset($_POST['request_withdrawal'])) {
    $amount = floatval($_POST['amount']);
    $upi_id = trim($_POST['upi_id']);
    $whatsapp = trim($_POST['whatsapp']);
    
    $errors = [];
    if ($amount <= 0) $errors[] = 'Amount must be greater than 0';
    if ($amount < $currency_settings['minimum_payout']) {
        $errors[] = 'Minimum withdrawal amount is ' . format_currency($currency_settings['minimum_payout']);
    }
    if ($amount > $available_balance) $errors[] = 'Amount exceeds available balance';
    if (empty($upi_id)) $errors[] = 'UPI ID is required';
    if (empty($whatsapp)) $errors[] = 'WhatsApp number is required';
    
    if (empty($errors)) {
        try {
            $stmt = $pdo->prepare('INSERT INTO withdrawals (user_id, amount, upi_id, whatsapp) VALUES (?, ?, ?, ?)');
            $stmt->execute([$user['id'], $amount, $upi_id, $whatsapp]);
            
            set_flash('<div class="alert alert-success">Withdrawal request submitted successfully! Admin will review and process it.</div>');
            header('Location: request_withdrawal.php');
            exit;
        } catch (Exception $e) {
            set_flash('<div class="alert alert-danger">Error submitting withdrawal request. Please try again.</div>');
        }
    } else {
        set_flash('<div class="alert alert-danger">' . implode(', ', $errors) . '</div>');
    }
}

// Fetch user's withdrawal history
$stmt = $pdo->prepare('
    SELECT * FROM withdrawals 
    WHERE user_id = ? 
    ORDER BY created_at DESC
');
$stmt->execute([$user['id']]);
$withdrawals = $stmt->fetchAll();

$message = get_flash();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Request Withdrawal - 1xreviews</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --bg-light: #f8fafc;
            --border-color: #e5e7eb;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-light);
            line-height: 1.6;
        }
        
        .navbar {
            background: white !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }
        
        .main-content {
            padding-top: 80px;
        }
        
        .card {
            border: none;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .card-header {
            background: white;
            border-bottom: 1px solid var(--border-color);
            border-radius: 16px 16px 0 0 !important;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .btn-primary {
            background: var(--primary-color);
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }
        
        .balance-card {
            background: linear-gradient(135deg, var(--success-color), #059669);
            color: white;
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .balance-amount {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem 1.25rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .form-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }
        
        .required-field::after {
            content: " *";
            color: var(--danger-color);
        }
        
        .withdrawal-card {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background: white;
            transition: all 0.3s ease;
        }
        
        .withdrawal-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 600;
        }
        
        .status-pending { background: #fef3c7; color: #92400e; }
        .status-approved { background: #dcfce7; color: #166534; }
        .status-rejected { background: #fecaca; color: #991b1b; }
        
        .table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .table th {
            background: var(--bg-light);
            border: none;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .table td {
            border: none;
            border-bottom: 1px solid var(--border-color);
            vertical-align: middle;
        }
        
        @media (max-width: 768px) {
            .balance-amount {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-star me-2"></i>1xreviews
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="<?php echo $user['role'] === 'reviewer' ? 'reviewer.php' : 'customer.php'; ?>">
                    <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                </a>
                <a class="nav-link" href="/logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="main-content">
        <div class="container py-4">
            <?php echo $message; ?>
            
            <!-- Balance Card -->
            <div class="balance-card">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h4 class="mb-2">Available for Withdrawal</h4>
                        <div class="balance-amount"><?php echo format_currency($available_balance); ?></div>
                        <p class="mb-0">Total earned: <?php echo format_currency($total_earnings); ?> | Already withdrawn: <?php echo format_currency($total_withdrawn); ?></p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex flex-column align-items-end">
                            <span class="badge bg-light text-dark mb-2">Minimum: <?php echo format_currency($currency_settings['minimum_payout']); ?></span>
                            <span class="badge bg-light text-dark">Processing: 24-48 hours</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Withdrawal Form -->
                <div class="col-lg-8">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>Request Withdrawal</h5>
                        </div>
                        <div class="card-body">
                            <?php if ($available_balance < $currency_settings['minimum_payout']): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                    <h5>Insufficient Balance</h5>
                                    <p class="text-muted">You need at least <?php echo format_currency($currency_settings['minimum_payout']); ?> to request a withdrawal</p>
                                    <p class="text-muted">Current balance: <?php echo format_currency($available_balance); ?></p>
                                </div>
                            <?php else: ?>
                                <form method="post">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label required-field">Amount (<?php echo $currency_settings['symbol']; ?>)</label>
                                            <input type="number" name="amount" class="form-control" min="<?php echo $currency_settings['minimum_payout']; ?>" max="<?php echo $available_balance; ?>" step="0.01" value="<?php echo min($currency_settings['minimum_payout'], $available_balance); ?>" required>
                                            <div class="form-text">Minimum: <?php echo format_currency($currency_settings['minimum_payout']); ?> | Maximum: <?php echo format_currency($available_balance); ?></div>
                                        </div>
                                        
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label required-field">UPI ID</label>
                                            <input type="text" name="upi_id" class="form-control" value="<?php echo htmlspecialchars($user['upi_id'] ?? ''); ?>" placeholder="username@upi" required>
                                            <div class="form-text">Your UPI ID for receiving payment</div>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label class="form-label required-field">WhatsApp Number</label>
                                        <input type="tel" name="whatsapp" class="form-control" value="<?php echo htmlspecialchars($user['whatsapp'] ?? ''); ?>" placeholder="+91 98765 43210" required>
                                        <div class="form-text">For payment confirmation and updates</div>
                                    </div>
                                    
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Important:</strong>
                                        <ul class="mb-0 mt-2">
                                            <li>Withdrawals are processed within 24-48 hours</li>
                                            <li>Payment will be sent to your UPI ID</li>
                                            <li>You'll receive confirmation on WhatsApp</li>
                                            <li>Minimum withdrawal amount is <?php echo format_currency($currency_settings['minimum_payout']); ?></li>
                                        </ul>
                                    </div>
                                    
                                    <div class="text-end">
                                        <button type="submit" name="request_withdrawal" class="btn btn-primary btn-lg">
                                            <i class="fas fa-paper-plane me-2"></i>Request Withdrawal
                                        </button>
                                    </div>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Withdrawal History -->
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-history me-2"></i>Withdrawal History</h5>
                        </div>
                        <div class="card-body p-0">
                            <?php if (empty($withdrawals)): ?>
                                <div class="p-4 text-center text-muted">
                                    <i class="fas fa-inbox fa-2x mb-3"></i>
                                    <h6>No withdrawals yet</h6>
                                    <p class="small">Your withdrawal history will appear here</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead>
                                            <tr>
                                                <th>Amount</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach (array_slice($withdrawals, 0, 5) as $w): ?>
                                                <tr>
                                                    <td class="fw-bold"><?php echo format_currency($w['amount']); ?></td>
                                                    <td>
                                                        <span class="status-badge status-<?php echo $w['status']; ?>">
                                                            <?php echo ucfirst($w['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td class="small">
                                                        <?php echo date('M j, Y', strtotime($w['created_at'])); ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <?php if (count($withdrawals) > 5): ?>
                                    <div class="p-3 text-center">
                                        <a href="#" class="btn btn-outline-primary btn-sm">View All</a>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const amount = parseFloat(document.querySelector('input[name="amount"]').value);
            const maxAmount = <?php echo $available_balance; ?>;
            const minAmount = <?php echo $currency_settings['minimum_payout']; ?>;
            
            if (amount < minAmount) {
                alert('Minimum withdrawal amount is <?php echo format_currency($currency_settings['minimum_payout']); ?>');
                e.preventDefault();
                return;
            }
            
            if (amount > maxAmount) {
                alert('Amount exceeds available balance');
                e.preventDefault();
                return;
            }
        });
    </script>
</body>
</html> 