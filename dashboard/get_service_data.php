<?php
require_once '../includes/auth.php';
require_login();
$user = current_user();
if ($user['role'] !== 'admin') {
    http_response_code(403);
    exit('Unauthorized');
}

require_once '../includes/db.php';

if (isset($_GET['service_id'])) {
    $service_id = intval($_GET['service_id']);
    
    try {
        $stmt = $pdo->prepare('SELECT * FROM services WHERE id = ?');
        $stmt->execute([$service_id]);
        $service = $stmt->fetch();
        
        if ($service) {
            header('Content-Type: application/json');
            echo json_encode($service);
        } else {
            http_response_code(404);
            echo json_encode(['error' => 'Service not found']);
        }
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Database error']);
    }
} else {
    http_response_code(400);
    echo json_encode(['error' => 'Service ID required']);
}
?> 