<?php
require_once '../includes/auth.php';
require_once '../includes/db.php';

header('Content-Type: application/json');

try {
    // Get count of available reviews
    $stmt = $pdo->prepare('
        SELECT COUNT(*) as count,
               MAX(amount) as latest_amount
        FROM reviews 
        WHERE status = "open" AND reviewer_id IS NULL
    ');
    $stmt->execute();
    $result = $stmt->fetch();
    
    echo json_encode([
        'success' => true,
        'count' => (int)$result['count'],
        'latest_amount' => (float)($result['latest_amount'] ?? 0)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Failed to get review count'
    ]);
}
?> 