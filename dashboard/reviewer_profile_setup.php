<?php
require_once '../includes/auth.php';
require_login();
$user = current_user();
if ($user['role'] !== 'reviewer') {
    header('Location: /index.php');
    exit;
}
require_once '../includes/db.php';

// Flash message helper
function set_flash($msg) { $_SESSION['flash'] = $msg; }
function get_flash() { $msg = $_SESSION['flash'] ?? ''; unset($_SESSION['flash']); return $msg; }

// Handle profile completion
if (isset($_POST['complete_profile'])) {
    $email = trim($_POST['email'] ?? '');
    $whatsapp = trim($_POST['whatsapp'] ?? '');
    $upi_id = trim($_POST['upi_id'] ?? '');
    $upi_qr = null;
    
    // Validate required fields
    $errors = [];
    if (empty($email)) $errors[] = 'Email is required';
    if (empty($whatsapp)) $errors[] = 'WhatsApp number is required';
    if (empty($upi_id)) $errors[] = 'UPI ID is required';
    
    // Handle UPI QR code upload
    if (!empty($_FILES['upi_qr']['name'])) {
        $target_dir = __DIR__ . '/../uploads/upi_qr/';
        if (!is_dir($target_dir)) mkdir($target_dir, 0777, true);
        $filename = uniqid('upi_') . '_' . basename($_FILES['upi_qr']['name']);
        $target_file = $target_dir . $filename;
        if (move_uploaded_file($_FILES['upi_qr']['tmp_name'], $target_file)) {
            $upi_qr = '/uploads/upi_qr/' . $filename;
        } else {
            $errors[] = 'Error uploading UPI QR code';
        }
    } else {
        $errors[] = 'UPI QR code is required';
    }
    
    if (empty($errors)) {
        try {
            // Update user profile
            $stmt = $pdo->prepare('UPDATE users SET email = ?, whatsapp = ?, upi_id = ?, upi_qr = ?, profile_completed = 1 WHERE id = ?');
            $stmt->execute([$email, $whatsapp, $upi_id, $upi_qr, $user['id']]);
            
            set_flash('<div class="alert alert-success">Profile completed successfully! You can now start working as a reviewer.</div>');
            header('Location: reviewer.php');
            exit;
        } catch (Exception $e) {
            set_flash('<div class="alert alert-danger">Error saving profile. Please try again.</div>');
        }
    } else {
        set_flash('<div class="alert alert-danger">' . implode(', ', $errors) . '</div>');
    }
}

$message = get_flash();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Your Profile - 1xreviews</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --bg-light: #f8fafc;
            --border-color: #e5e7eb;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-light);
            line-height: 1.6;
        }
        
        .navbar {
            background: white !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
        }
        
        .main-content {
            padding-top: 80px;
            min-height: calc(100vh - 80px);
            display: flex;
            align-items: center;
        }
        
        .profile-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            padding: 3rem;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .profile-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .profile-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }
        
        .form-control, .form-select {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem 1.25rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        
        .form-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }
        
        .required-field::after {
            content: " *";
            color: var(--danger-color);
        }
        
        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--bg-light);
        }
        
        .upload-area:hover {
            border-color: var(--primary-color);
            background: #eff6ff;
        }
        
        .upload-area.dragover {
            border-color: var(--primary-color);
            background: #eff6ff;
        }
        
        .btn-primary {
            background: var(--primary-color);
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }
        
        .info-card {
            background: #eff6ff;
            border: 1px solid #dbeafe;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .preview-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 8px;
            margin-top: 1rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--border-color);
            color: var(--text-light);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 0.5rem;
        }
        
        .step.active {
            background: var(--primary-color);
            color: white;
        }
        
        .step.completed {
            background: var(--success-color);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-star me-2"></i>1xreviews
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-user me-2"></i><?php echo htmlspecialchars($user['name']); ?>
                </span>
            </div>
        </div>
    </nav>

    <div class="main-content">
        <div class="container">
            <div class="profile-card">
                <div class="profile-header">
                    <div class="profile-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h2 class="mb-2">Complete Your Profile</h2>
                    <p class="text-muted">Please provide your contact and payment information to start working as a reviewer</p>
                </div>
                
                <div class="step-indicator">
                    <div class="step active">1</div>
                    <div class="step">2</div>
                    <div class="step">3</div>
                </div>
                
                <?php echo $message; ?>
                
                <div class="info-card">
                    <h6><i class="fas fa-info-circle me-2"></i>Why do we need this information?</h6>
                    <ul class="mb-0 small">
                        <li><strong>Email:</strong> For important notifications and account updates</li>
                        <li><strong>WhatsApp:</strong> For quick communication about reviews and payments</li>
                        <li><strong>UPI ID:</strong> To send your earnings directly to your account</li>
                        <li><strong>UPI QR Code:</strong> For verification and faster payments</li>
                    </ul>
                </div>
                
                <form method="post" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label required-field">Email Address</label>
                            <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                            <div class="form-text">We'll use this for important notifications</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label required-field">WhatsApp Number</label>
                            <input type="tel" name="whatsapp" class="form-control" placeholder="+91 98765 43210" required>
                            <div class="form-text">Include country code for international numbers</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label required-field">UPI ID</label>
                            <input type="text" name="upi_id" class="form-control" placeholder="username@upi" required>
                            <div class="form-text">Your UPI ID for receiving payments</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label required-field">UPI QR Code</label>
                            <div class="upload-area" onclick="document.getElementById('upiQr').click()">
                                <i class="fas fa-qrcode fa-2x text-muted mb-3"></i>
                                <h6>Upload UPI QR Code</h6>
                                <p class="text-muted small">Click to upload or drag & drop</p>
                                <p class="text-muted small">PNG, JPG up to 2MB</p>
                            </div>
                            <input type="file" name="upi_qr" id="upiQr" class="d-none" accept="image/*" required>
                            <div id="upiQrPreview"></div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Important:</strong> All fields are mandatory. You cannot start working as a reviewer until your profile is complete.
                    </div>
                    
                    <div class="text-center">
                        <button type="submit" name="complete_profile" class="btn btn-primary btn-lg">
                            <i class="fas fa-check me-2"></i>Complete Profile & Start Working
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Handle UPI QR code upload
        document.getElementById('upiQr').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('upiQrPreview');
                    preview.innerHTML = `<img src="${e.target.result}" class="preview-image">`;
                };
                reader.readAsDataURL(file);
            }
        });
        
        // Drag and drop functionality
        const uploadArea = document.querySelector('.upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('upiQr').files = files;
                const event = new Event('change');
                document.getElementById('upiQr').dispatchEvent(event);
            }
        });
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const email = document.querySelector('input[name="email"]').value;
            const whatsapp = document.querySelector('input[name="whatsapp"]').value;
            const upiId = document.querySelector('input[name="upi_id"]').value;
            const upiQr = document.querySelector('input[name="upi_qr"]').files[0];
            
            if (!email || !whatsapp || !upiId || !upiQr) {
                e.preventDefault();
                alert('Please fill in all required fields and upload your UPI QR code.');
                return false;
            }
        });
    </script>
</body>
</html> 