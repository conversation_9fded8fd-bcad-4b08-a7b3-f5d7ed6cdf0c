<!-- Enhanced Claimed Reviews with Filter Tabs -->
<?php if (!empty($claimed_reviews)): ?>
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-tasks me-2"></i>My Claimed Reviews</h5>
            <div class="review-filter-tabs">
                <button class="filter-tab active" data-filter="all">All</button>
                <button class="filter-tab" data-filter="pending">Pending</button>
                <button class="filter-tab" data-filter="completed">Completed</button>
                <button class="filter-tab" data-filter="expired">Expired</button>
            </div>
        </div>
    </div>
    <div class="card-body">
        <?php foreach ($claimed_reviews as $review): ?>
            <div class="review-card enhanced-review-card" data-status="<?php echo $review['timer_status']; ?>">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div class="platform-badge platform-<?php echo strtolower($review['platform']); ?>">
                        <i class="fab fa-<?php echo strtolower($review['platform']); ?> me-2"></i>
                        <?php echo htmlspecialchars($review['platform']); ?>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <div class="earnings-badge">
                            Earn: $<?php echo number_format($review['amount'] * 0.8, 2); ?>
                        </div>
                        <span class="status-badge status-<?php echo $review['timer_status']; ?>">
                            <?php echo ucfirst($review['timer_status']); ?>
                        </span>
                        <?php if ($review['timer_status'] === 'active'): ?>
                            <div class="timer-badge-15min" data-claimed-at="<?php echo $review['claimed_at']; ?>">
                                <i class="fas fa-clock me-1"></i>
                                <span class="timer-text-15min">15:00</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <h6 class="mb-2"><?php echo htmlspecialchars($review['business_name']); ?></h6>
                <p class="text-muted mb-3">
                    <i class="fas fa-user me-2"></i>Posted by: <?php echo htmlspecialchars($review['customer_name']); ?>
                    <span class="ms-3"><i class="fas fa-calendar me-1"></i>Claimed: <?php echo date('M j, g:i A', strtotime($review['claimed_at'])); ?></span>
                </p>
                
                <div class="review-content">
                    <button class="copy-btn" onclick="copyContent('<?php echo addslashes($review['review_text']); ?>')">
                        <i class="fas fa-copy me-1"></i>Copy
                    </button>
                    <p class="mb-0"><?php echo nl2br(htmlspecialchars($review['review_text'])); ?></p>
                </div>
                
                <!-- Action Buttons -->
                <div class="d-flex gap-2 mt-3">
                    <?php if (isset($review['business_link']) && $review['business_link']): ?>
                        <a href="<?php echo htmlspecialchars($review['business_link']); ?>" target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-external-link-alt me-2"></i>Open Rating Page
                        </a>
                    <?php endif; ?>
                    
                    <?php if ($review['timer_status'] === 'active'): ?>
                        <!-- Mark as Completed Button -->
                        <button class="btn btn-success btn-sm" onclick="markAsCompleted(<?php echo $review['id']; ?>)">
                            <i class="fas fa-check me-2"></i>Mark as Completed
                        </button>
                        
                        <!-- Submit Proof Button -->
                        <button class="btn btn-warning btn-sm" onclick="openProofModal(<?php echo $review['id']; ?>)">
                            <i class="fas fa-upload me-2"></i>Submit Proof
                        </button>
                    <?php endif; ?>
                    
                    <?php if ($review['status'] === 'proof_submitted'): ?>
                        <span class="badge bg-info">
                            <i class="fas fa-clock me-1"></i>Proof Submitted - Waiting for Review
                        </span>
                    <?php endif; ?>
                </div>
                
                <!-- Proof Status Display -->
                <?php if (!empty($review['proof_screenshot']) || !empty($review['proof_link'])): ?>
                    <div class="proof-status-card mt-3">
                        <h6><i class="fas fa-paperclip me-2"></i>Submitted Proof</h6>
                        <?php if (!empty($review['proof_screenshot'])): ?>
                            <p class="mb-1"><i class="fas fa-image me-2"></i>Screenshot uploaded</p>
                        <?php endif; ?>
                        <?php if (!empty($review['proof_link'])): ?>
                            <p class="mb-1"><i class="fas fa-link me-2"></i>Review Link: 
                                <a href="<?php echo htmlspecialchars($review['proof_link']); ?>" target="_blank" class="text-primary">View Review</a>
                            </p>
                        <?php endif; ?>
                        <small class="text-muted">Submitted: <?php echo isset($review['proof_submitted_at']) ? date('M j, g:i A', strtotime($review['proof_submitted_at'])) : 'Unknown'; ?></small>
                    </div>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </div>
</div>
<?php endif; ?>

<!-- Submit Proof Modal -->
<div class="modal fade" id="submitProofModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-upload me-2"></i>Submit Review Proof</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" enctype="multipart/form-data" id="proofSubmissionForm">
                <div class="modal-body">
                    <input type="hidden" name="review_id" id="proofReviewId">
                    
                    <div class="mb-4">
                        <label class="form-label fw-bold">Review Screenshot *</label>
                        <div class="proof-upload-area" onclick="document.getElementById('proofScreenshot').click()">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h5>Upload Screenshot</h5>
                            <p class="text-muted">Upload a screenshot of your posted review</p>
                            <p class="text-muted small">PNG, JPG, WebP up to 5MB</p>
                        </div>
                        <input type="file" name="proof_screenshot" id="proofScreenshot" class="d-none" accept="image/*" required>
                        <div id="proofScreenshotPreview" class="mt-3"></div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="form-label fw-bold">Review Link (Optional)</label>
                        <input type="url" name="review_link" id="proofReviewLink" class="form-control" placeholder="https://example.com/your-review">
                        <div class="form-text">Paste the direct link to your review if available</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Verification Requirements:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Screenshot must clearly show the posted review</li>
                            <li>Your username/profile should be visible</li>
                            <li>Review text should match exactly</li>
                            <li>Admin will verify within 24 hours</li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="submit_proof" class="btn btn-warning">
                        <i class="fas fa-upload me-2"></i>Submit Proof
                    </button>
                </div>
            </form>
        </div>
    </div>
</div> 