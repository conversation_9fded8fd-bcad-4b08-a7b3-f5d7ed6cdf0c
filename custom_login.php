<?php
session_start();
require_once 'includes/db.php';

// Flash message helper
function set_flash($msg) { $_SESSION['flash'] = $msg; }
function get_flash() { $msg = $_SESSION['flash'] ?? ''; unset($_SESSION['flash']); return $msg; }

// Handle login
if (isset($_POST['login'])) {
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    
    // Check for admin login
    if ($email === '<EMAIL>' && $password === 'Harman 1313@') {
        // Check if user exists
        $stmt = $pdo->prepare('SELECT * FROM users WHERE email = ?');
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if (!$user) {
            // Create admin user if not exists
            $stmt = $pdo->prepare('INSERT INTO users (name, email, role, status, profile_completed) VALUES (?, ?, "admin", "active", 1)');
            $stmt->execute(['Admin User', $email]);
            $user_id = $pdo->lastInsertId();
            
            // Fetch the created user
            $stmt = $pdo->prepare('SELECT * FROM users WHERE id = ?');
            $stmt->execute([$user_id]);
            $user = $stmt->fetch();
        } else {
            // Update existing user to admin role if not already admin
            if ($user['role'] !== 'admin') {
                $stmt = $pdo->prepare('UPDATE users SET role = "admin", status = "active", profile_completed = 1 WHERE id = ?');
                $stmt->execute([$user['id']]);
                
                // Fetch updated user
                $stmt = $pdo->prepare('SELECT * FROM users WHERE id = ?');
                $stmt->execute([$user['id']]);
                $user = $stmt->fetch();
            }
        }
        
        // Set session
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['role'] = $user['role'];
        
        // Redirect to admin dashboard
        header('Location: dashboard/admin.php');
        exit;
    }
    
    // Check for regular user login
    $stmt = $pdo->prepare('SELECT * FROM users WHERE email = ? AND status != "banned"');
    $stmt->execute([$email]);
    $user = $stmt->fetch();
    
    if ($user) {
        // For now, we'll use a simple password check
        // In production, you should use password_hash() and password_verify()
        if ($password === 'Harman 1313@') {
            // Set session
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['role'] = $user['role'];
            
            // Redirect based on role
            if ($user['role'] === 'admin') {
                header('Location: dashboard/admin.php');
            } elseif ($user['role'] === 'reviewer') {
                if ($user['profile_completed']) {
                    header('Location: dashboard/reviewer.php');
                } else {
                    header('Location: dashboard/reviewer_profile_setup.php');
                }
            } else {
                header('Location: dashboard/customer.php');
            }
            exit;
        }
    }
    
    set_flash('<div class="alert alert-danger">Invalid email or password.</div>');
}

$message = get_flash();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - 1xreviews</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --bg-light: #f8fafc;
            --border-color: #e5e7eb;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-header h2 {
            margin: 0;
            font-weight: 700;
            font-size: 1.75rem;
        }
        
        .login-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-control {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem 1.25rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--bg-light);
        }
        
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            background: white;
        }
        
        .form-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }
        
        .btn-primary {
            background: var(--primary-color);
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }
        
        .input-group-text {
            background: var(--bg-light);
            border: 2px solid var(--border-color);
            border-right: none;
            border-radius: 12px 0 0 12px;
            color: var(--text-light);
        }
        
        .input-group .form-control {
            border-left: none;
            border-radius: 0 12px 12px 0;
        }
        
        .input-group:focus-within .input-group-text {
            border-color: var(--primary-color);
            background: white;
        }
        
        .demo-credentials {
            background: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem;
            margin-top: 1rem;
        }
        
        .demo-credentials h6 {
            color: var(--text-dark);
            margin-bottom: 0.5rem;
            font-weight: 600;
        }
        
        .demo-credentials p {
            margin: 0;
            font-size: 0.875rem;
            color: var(--text-light);
        }
        
        .demo-credentials strong {
            color: var(--primary-color);
        }
        
        .back-link {
            text-align: center;
            margin-top: 1rem;
        }
        
        .back-link a {
            color: var(--text-light);
            text-decoration: none;
            font-size: 0.875rem;
        }
        
        .back-link a:hover {
            color: var(--primary-color);
        }
        
        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
                border-radius: 16px;
            }
            
            .login-header {
                padding: 1.5rem;
            }
            
            .login-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h2><i class="fas fa-star me-2"></i>1xreviews</h2>
            <p>Admin & User Login</p>
        </div>
        
        <div class="login-body">
            <?php echo $message; ?>
            
            <form method="post">
                <div class="mb-3">
                    <label class="form-label">Email Address</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-envelope"></i>
                        </span>
                        <input type="email" name="email" class="form-control" placeholder="Enter your email" required>
                    </div>
                </div>
                
                <div class="mb-4">
                    <label class="form-label">Password</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <input type="password" name="password" class="form-control" placeholder="Enter your password" required>
                    </div>
                </div>
                
                <button type="submit" name="login" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>Login
                </button>
            </form>
            
            <div class="demo-credentials">
                <h6><i class="fas fa-info-circle me-2"></i>Demo Credentials</h6>
                <p><strong>Admin:</strong> <EMAIL></p>
                <p><strong>Password:</strong> Harman 1313@</p>
                <p class="mt-2 small text-muted">This will create an admin account if it doesn't exist.</p>
            </div>
            
            <div class="back-link">
                <a href="index.php">
                    <i class="fas fa-arrow-left me-1"></i>Back to Home
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 