-- Add index to users.email for fast lookups
ALTER TABLE users ADD INDEX idx_email (email);

-- Add indexes to reviews for common queries
ALTER TABLE reviews ADD INDEX idx_customer_id (customer_id);
ALTER TABLE reviews ADD INDEX idx_reviewer_id (reviewer_id);
ALTER TABLE reviews ADD INDEX idx_status (status);

-- Add index to wallet_transactions.user_id
ALTER TABLE wallet_transactions ADD INDEX idx_user_id (user_id);

-- Add index to withdrawals.user_id
ALTER TABLE withdrawals ADD INDEX idx_user_id (user_id);

-- Add column to reviews table
ALTER TABLE reviews ADD COLUMN claimed_at DATETIME DEFAULT NULL; 