<?php
// error.php - Centralized error handling

// Get error code from URL parameter or default to 500
$error_code = isset($_GET['code']) ? (int)$_GET['code'] : 500;
$error_message = $_GET['message'] ?? '';

// Error configurations
$errors = [
    400 => ['title' => 'Bad Request', 'description' => 'The request could not be understood by the server.'],
    401 => ['title' => 'Unauthorized', 'description' => 'You need to log in to access this page.'],
    403 => ['title' => 'Forbidden', 'description' => 'You don\'t have permission to access this resource.'],
    404 => ['title' => 'Page Not Found', 'description' => 'The page you\'re looking for doesn\'t exist.'],
    500 => ['title' => 'Server Error', 'description' => 'Something went wrong on our end. Please try again later.'],
    503 => ['title' => 'Service Unavailable', 'description' => 'The service is temporarily unavailable. Please try again later.']
];

$error = $errors[$error_code] ?? $errors[500];
http_response_code($error_code);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $error['title']; ?> - 1xreviews</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .error-container {
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
            padding: 2rem;
        }
        .error-code {
            font-size: 8rem;
            font-weight: 800;
            line-height: 1;
            opacity: 0.8;
            margin-bottom: 1rem;
        }
        .error-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        .error-description {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        .btn-home {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 30px;
            font-weight: 600;
            border-radius: 50px;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .btn-home:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: translateY(-2px);
        }
        .error-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-container">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="error-code"><?php echo $error_code; ?></div>
            <h1 class="error-title"><?php echo $error['title']; ?></h1>
            <p class="error-description">
                <?php echo $error_message ?: $error['description']; ?>
            </p>
            <div class="error-actions">
                <a href="/" class="btn-home">
                    <i class="fas fa-home me-2"></i>Back to Home
                </a>
            </div>
        </div>
    </div>
</body>
</html> 