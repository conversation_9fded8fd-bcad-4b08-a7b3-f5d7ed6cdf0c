<!-- Supported Platforms Section -->
<section class="platforms-section py-5 py-md-6" id="platforms">
    <div class="container">
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 text-center">
                <div class="section-badge mb-3">
                    <i class="fas fa-th-large me-2"></i>
                    <span>Supported Platforms</span>
                </div>
                <h2 class="section-title mb-4">
                    Review on Your Favorite 
                    <span class="text-primary">Platforms</span>
                </h2>
                <p class="section-description">
                    Choose from a wide range of popular review platforms and start earning money for your honest feedback.
                </p>
            </div>
        </div>
        
        <div class="row g-4 justify-content-center">
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="platform-card-modern">
                    <div class="platform-logo-modern">
                        <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="32" cy="32" r="32" fill="#4285F4"/>
                            <path d="M32 8C18.745 8 8 18.745 8 32s10.745 24 24 24 24-10.745 24-24S45.255 8 32 8zm0 44c-11.046 0-20-8.954-20-20S20.954 12 32 12s20 8.954 20 20-8.954 20-20 20z" fill="white"/>
                            <path d="M32 16c-8.837 0-16 7.163-16 16s7.163 16 16 16 16-7.163 16-16-7.163-16-16-16zm0 28c-6.627 0-12-5.373-12-12s5.373-12 12-12 12 5.373 12 12-5.373 12-12 12z" fill="white"/>
                            <path d="M32 24c-4.418 0-8 3.582-8 8s3.582 8 8 8 8-3.582 8-8-3.582-8-8-8zm0 12c-2.209 0-4-1.791-4-4s1.791-4 4-4 4 1.791 4 4-1.791 4-4 4z" fill="white"/>
                        </svg>
                    </div>
                    <h5 class="platform-name-modern">Google Reviews</h5>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="platform-card-modern">
                    <div class="platform-logo-modern">
                        <img src="https://cdn.trustpilot.net/brand-assets/4.1.0/logo-white.svg" alt="Trustpilot" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiMwMEM5NEYiLz4KPHN0YXIgY3g9IjI0IiBjeT0iMjQiIHI9IjEwIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K'">
                    </div>
                    <h5 class="platform-name-modern">Trustpilot</h5>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="platform-card-modern">
                    <div class="platform-logo-modern">
                        <img src="https://static.tacdn.com/img2/brand_refresh/Tripadvisor_lockup_horizontal_secondary_registered.svg" alt="TripAdvisor" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjQiIGN5PSIyNCIgcj0iMjQiIGZpbGw9IiMwMEM5NEYiLz4KPHN0YXIgY3g9IjI0IiBjeT0iMjQiIHI9IjEwIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K'">
                    </div>
                    <h5 class="platform-name-modern">TripAdvisor</h5>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="platform-card-modern">
                    <div class="platform-logo-modern">
                        <img src="https://upload.wikimedia.org/wikipedia/commons/0/09/YouTube_full-color_icon_%282017%29.svg" alt="YouTube">
                    </div>
                    <h5 class="platform-name-modern">YouTube</h5>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="platform-card-modern">
                    <div class="platform-logo-modern">
                        <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/a/a9/Amazon_logo.svg/200px-Amazon_logo.svg.png" alt="Amazon">
                    </div>
                    <h5 class="platform-name-modern">Amazon</h5>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="platform-card-modern">
                    <div class="platform-logo-modern">
                        <img src="https://upload.wikimedia.org/wikipedia/en/0/04/Facebook_f_logo_%282021%29.svg" alt="Facebook" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNDgiIHZpZXdCb3g9IjAgMCA0OCA0OCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjQ4IiByeD0iMTIiIGZpbGw9IiMxODc3RjIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIxMiIgeT0iMTIiPgo8cGF0aCBkPSJNOS4xMDEgMjMuNjkxVjEzLjY0N0g2LjY5M1Y5LjkyNEg5LjEwMVY3LjI1NEM5LjEwMSA0Ljc5NSAxMC43MDQgMy4zMDkgMTMuNzggMy4zMDlDMTUuNjY3IDMuMzA5IDE2LjI0OCAzLjUxOSAxNi4yNDggMy41MTlMMTUuNjMzIDYuOTU3Qzk1LjY0IDYuOTU3IDE0Ljk2OSA2Ljk1NyAxNC45NjkgNi45NTdDMTQuMjkzIDYuOTU3IDEzLjY0NSA2Ljk1NyAxMy42NDUgOC4xODVWOS45MjRIMTUuNjU3QzE1LjU1NSAxMC42MjEgMTUuNDAzIDEyLjI1MiAxNS4zIDEzLjY0N0gxMy42NDVWMjMuNjkxSDE1LjY1N1Y5LjkyNFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo8L3N2Zz4K'">
                    </div>
                    <h5 class="platform-name-modern">Facebook</h5>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="platform-card-modern">
                    <div class="platform-logo-modern">
                        <img src="https://upload.wikimedia.org/wikipedia/en/a/a9/TikTok_logo.svg" alt="TikTok">
                    </div>
                    <h5 class="platform-name-modern">TikTok</h5>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Simple Transparent Navigation -->
<nav class="navbar navbar-expand-lg fixed-top">
    <div class="container">
        <!-- Logo -->
        <a class="navbar-brand text-white fw-bold fs-4" href="#hero">
            <i class="fas fa-star me-2"></i>1xreviews
        </a>
        
        <!-- Hamburger Menu -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <i class="fas fa-bars text-white"></i>
        </button>
        
        <!-- Navigation Menu -->
        <div class="collapse navbar-collapse justify-content-center" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link text-white px-3" href="#hero">Home</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-white px-3" href="#about">About</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-white px-3" href="#services">Services</a>
                </li>
            </ul>
            
            <!-- Buttons (Hidden on Mobile) -->
            <div class="d-none d-lg-flex gap-2 ms-auto">
                <button class="btn btn-outline-light rounded-pill px-3 py-2" onclick="showLoginModal('reviewer')">
                    Login
                </button>
                <button class="btn btn-light rounded-pill px-3 py-2" onclick="showLoginModal('customer')">
                    Sign Up
                </button>
            </div>
        </div>
    </div>
</nav>

<style>
.navbar {
    background: transparent !important;
    z-index: 1051;
    padding: 1rem 0;
    transition: background 0.3s;
}

.navbar.scrolled {
    background: rgba(0, 0, 0, 0.8) !important;
    backdrop-filter: blur(10px);
}

.navbar-brand {
    color: #fff !important;
    font-weight: 700;
}

.navbar-toggler {
    border: none;
    padding: 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler i {
    color: #fff !important;
    font-size: 1.5rem;
}

.nav-link {
    color: #fff !important;
    font-weight: 500;
    transition: opacity 0.2s;
}

.nav-link:hover {
    opacity: 0.8;
}

.btn {
    font-weight: 600;
    transition: all 0.2s;
}

.btn:hover {
    transform: translateY(-2px);
}

/* Mobile Styles */
@media (max-width: 991.98px) {
    .navbar-collapse {
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        margin-top: 1rem;
        padding: 1rem;
    }
    
    .navbar-nav {
        text-align: center;
    }
    
    .nav-item {
        margin: 0.5rem 0;
    }
    
    /* Show buttons in mobile menu */
    .d-none.d-lg-flex {
        display: flex !important;
        flex-direction: column;
        width: 100%;
        margin-top: 1rem;
    }
    
    .btn {
        width: 100%;
        margin: 0.25rem 0;
    }
}
</style>

<script>
// Toggle scrolled class on scroll
window.addEventListener('scroll', function() {
    var navbar = document.querySelector('.navbar');
    if (window.scrollY > 10) {
        navbar.classList.add('scrolled');
    } else {
        navbar.classList.remove('scrolled');
    }
});
</script> 