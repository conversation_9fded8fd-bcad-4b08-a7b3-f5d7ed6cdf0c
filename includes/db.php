<?php
// includes/db.php

// Environment variables or secure config
$host = $_ENV['DB_HOST'] ?? getenv('DB_HOST') ?: 'srv875.hstgr.io';
$db   = $_ENV['DB_NAME'] ?? getenv('DB_NAME') ?: 'u224750715_review';
$user = $_ENV['DB_USER'] ?? getenv('DB_USER') ?: 'u224750715_review';
$pass = $_ENV['DB_PASS'] ?? getenv('DB_PASS') ?: 'Harman1313@@';
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;dbname=$db;charset=$charset";
$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
    PDO::ATTR_PERSISTENT         => false,
    PDO::ATTR_TIMEOUT            => 30,
];

function get_db() {
    global $dsn, $options, $user, $pass;
    static $pdo;
    
    if ($pdo === null) {
        try {
            $pdo = new PDO($dsn, $user, $pass, $options);
        } catch (PDOException $e) {
            // Log error securely without exposing credentials
            error_log("Database connection failed: " . $e->getMessage());
            
            // Return user-friendly error in production
            if (defined('ENVIRONMENT') && ENVIRONMENT === 'production') {
                throw new Exception("Database connection unavailable. Please try again later.");
            } else {
                throw new PDOException("Database connection failed: " . $e->getMessage(), (int)$e->getCode());
            }
        }
    }
    
    return $pdo;
}

// Initialize global PDO connection
try {
    $pdo = get_db();
} catch (Exception $e) {
    // Handle gracefully
    error_log("Database initialization error: " . $e->getMessage());
    $pdo = null;
}
