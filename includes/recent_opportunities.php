<?php
// Fetch recent review opportunities from database
$recent_opportunities = [];
try {
    $pdo = get_db();
    if ($pdo) {
        $stmt = $pdo->query("SELECT r.*, u.name as customer_name, 
                            (SELECT COUNT(*) FROM reviews r2 WHERE r2.customer_id = r.customer_id AND r2.status = 'claimed') as claimed_count
                            FROM reviews r 
                            LEFT JOIN users u ON r.customer_id = u.id 
                            WHERE r.status = 'open' 
                            ORDER BY r.created_at DESC 
                            LIMIT 6");
        $recent_opportunities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (Exception $e) {
    error_log("Failed to fetch recent opportunities: " . $e->getMessage());
}

// Fallback data if database error or no data
if (empty($recent_opportunities)) {
    $recent_opportunities = [
        ['business_name' => 'TechStart Solutions', 'platform' => 'Google', 'amount' => 25, 'created_at' => '2024-01-15 10:30:00', 'claimed_count' => 12, 'review_text' => 'Looking for authentic reviews for our new project management software. Experience with similar tools preferred.'],
        ['business_name' => 'Fresh Bites Cafe', 'platform' => 'Yelp', 'amount' => 20, 'created_at' => '2024-01-15 09:15:00', 'claimed_count' => 8, 'review_text' => 'New organic cafe in downtown. Looking for honest reviews about our food quality and service experience.'],
        ['business_name' => 'AutoCare Pro', 'platform' => 'Trustpilot', 'amount' => 30, 'created_at' => '2024-01-15 08:45:00', 'claimed_count' => 15, 'review_text' => 'Professional auto repair services. Need reviews from customers who have used our maintenance services.'],
        ['business_name' => 'Dream Homes Realty', 'platform' => 'Google', 'amount' => 40, 'created_at' => '2024-01-15 07:30:00', 'claimed_count' => 6, 'review_text' => 'Real estate agency looking for reviews from clients who have bought or sold properties through us.'],
        ['business_name' => 'Digital Marketing Pro', 'platform' => 'Clutch', 'amount' => 35, 'created_at' => '2024-01-15 06:20:00', 'claimed_count' => 10, 'review_text' => 'Digital marketing agency seeking reviews from businesses we\'ve helped grow their online presence.'],
        ['business_name' => 'Wellness Center', 'platform' => 'TripAdvisor', 'amount' => 18, 'created_at' => '2024-01-15 05:10:00', 'claimed_count' => 18, 'review_text' => 'Wellness center offering yoga, massage, and holistic treatments. Looking for authentic customer reviews.']
    ];
}

// Helper function to get time ago
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    $time = ($time < 1) ? 1 : $time;
    $tokens = array(
        31536000 => 'year',
        2592000 => 'month',
        604800 => 'week',
        86400 => 'day',
        3600 => 'hour',
        60 => 'minute',
        1 => 'second'
    );

    foreach ($tokens as $unit => $text) {
        if ($time < $unit) continue;
        $numberOfUnits = floor($time / $unit);
        return $numberOfUnits . ' ' . $text . (($numberOfUnits > 1) ? 's' : '') . ' ago';
    }
}

// Helper function to get business category based on business name
function getBusinessCategory($businessName) {
    $categories = [
        'tech' => ['tech', 'software', 'digital', 'app', 'web', 'mobile', 'development'],
        'food' => ['cafe', 'restaurant', 'food', 'bites', 'kitchen', 'dining'],
        'automotive' => ['auto', 'car', 'vehicle', 'repair', 'service', 'maintenance'],
        'real_estate' => ['realty', 'homes', 'property', 'estate', 'housing'],
        'marketing' => ['marketing', 'advertising', 'promotion', 'brand'],
        'wellness' => ['wellness', 'health', 'fitness', 'yoga', 'massage', 'therapy'],
        'retail' => ['store', 'shop', 'retail', 'boutique', 'market'],
        'services' => ['service', 'consulting', 'agency', 'professional']
    ];
    
    $businessName = strtolower($businessName);
    foreach ($categories as $category => $keywords) {
        foreach ($keywords as $keyword) {
            if (strpos($businessName, $keyword) !== false) {
                return ucfirst(str_replace('_', ' ', $category));
            }
        }
    }
    return 'Business Services';
}

// Helper function to get business icon
function getBusinessIcon($businessName) {
    $icons = [
        'tech' => 'fas fa-laptop',
        'food' => 'fas fa-utensils',
        'automotive' => 'fas fa-car',
        'real_estate' => 'fas fa-home',
        'marketing' => 'fas fa-bullhorn',
        'wellness' => 'fas fa-heartbeat',
        'retail' => 'fas fa-shopping-bag',
        'services' => 'fas fa-briefcase'
    ];
    
    $category = strtolower(getBusinessCategory($businessName));
    foreach ($icons as $key => $icon) {
        if (strpos($category, $key) !== false) {
            return $icon;
        }
    }
    return 'fas fa-store';
}
?>

<!-- Latest Opportunities Section -->
<section class="py-5 py-md-6 bg-light" id="recent-opportunities">
    <div class="container">
        <div class="row justify-content-center mb-4">
            <div class="col-lg-8 text-center">
                <div class="section-badge mb-3">
                    <i class="fas fa-briefcase me-2"></i>
                    <span>Latest Opportunities</span>
                </div>
                <h2 class="section-title mb-4">Recent Review Requests</h2>
                <p class="section-description">Claim these review requests before they're gone</p>
            </div>
        </div>
        
        <div class="row g-3">
            <?php foreach (array_slice($recent_opportunities, 0, 6) as $opportunity): ?>
            <div class="col-lg-4 col-md-6">
                <div class="opportunity-card-modern">
                    <div class="opportunity-main">
                        <div class="opportunity-header-modern">
                            <div class="business-info-modern">
                                <i class="<?php echo getBusinessIcon($opportunity['business_name']); ?> me-2" style="font-size: 1.2rem; color: #6b7280;"></i>
                                <div class="business-details-modern">
                                    <h6 class="business-name-modern"><?php echo htmlspecialchars($opportunity['business_name']); ?></h6>
                                    <div class="platform-badge-modern">
                                        <?php
                                        $platformIcons = [
                                            'Google' => ['fab fa-google', '#4285F4'],
                                            'Trustpilot' => ['fas fa-thumbs-up', '#00b67a'],
                                            'Yelp' => ['fab fa-yelp', '#ff1a1a'],
                                            'Amazon' => ['fab fa-amazon', '#ff9900'],
                                            'TripAdvisor' => ['fas fa-plane', '#00af87'],
                                            'Clutch' => ['fas fa-handshake', '#ff6b35'],
                                            'Facebook' => ['fab fa-facebook', '#1877f2'],
                                            'Instagram' => ['fab fa-instagram', '#e4405f']
                                        ];
                                        $platform = $opportunity['platform'];
                                        $icon = isset($platformIcons[$platform]) ? $platformIcons[$platform][0] : 'fas fa-star';
                                        $color = isset($platformIcons[$platform]) ? $platformIcons[$platform][1] : '#000';
                                        ?>
                                        <i class="<?php echo $icon; ?>" style="color: <?php echo $color; ?>;"></i>
                                        <span><?php echo $platform; ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="reward-badge-modern">
                                <span class="reward-amount-modern">₹<?php echo number_format($opportunity['amount'] * 25, 0); ?></span>
                            </div>
                        </div>
                        <div class="opportunity-meta-modern">
                            <span class="time-badge-modern">
                                <i class="fas fa-clock"></i>
                                <?php echo timeAgo($opportunity['created_at']); ?>
                            </span>
                            <span class="claimed-badge-modern">
                                <i class="fas fa-users"></i>
                                <?php echo $opportunity['claimed_count']; ?> claimed
                            </span>
                        </div>
                    </div>
                    
                    <!-- Lock Overlay -->
                    <div class="lock-overlay-modern">
                        <div class="lock-content-modern">
                            <i class="fas fa-lock"></i>
                            <span>Login to claim</span>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <!-- View More Button -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <button class="btn btn-primary btn-lg" onclick="showLoginModal('reviewer')">
                    <i class="fas fa-search me-2"></i>Browse All Opportunities
                </button>
            </div>
        </div>
    </div>
</section> 