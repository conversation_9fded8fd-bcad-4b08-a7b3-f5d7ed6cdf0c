<?php
require_once 'auth.php';
require_once 'db.php';

if (is_logged_in()) {
    $user = current_user();
    if ($user['role'] === 'admin') header('Location: /dashboard/admin.php');
    elseif ($user['role'] === 'reviewer') header('Location: /dashboard/reviewer.php');
    else header('Location: /dashboard/customer.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- SEO Meta Tags -->
    <title>1xreviews - Get Paid for Writing Reviews | Earn Money Online</title>
    <meta name="description" content="Join 1xreviews and earn money by writing honest reviews. Get paid for your opinions on products and services. Start earning today!">
    <meta name="keywords" content="paid reviews, earn money online, review writing, get paid for reviews, 1xreviews, review marketplace">
    <meta name="author" content="1xreviews">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    
    <!-- Theme Colors -->
    <meta name="theme-color" content="#1877f2">
    <meta name="msapplication-TileColor" content="#1877f2">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="1xreviews - Get Paid for Writing Reviews">
    <meta property="og:description" content="Join 1xreviews and earn money by writing honest reviews. Get paid for your opinions on products and services.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://1xreviews.com/">
    <meta property="og:image" content="https://1xreviews.com/assets/images/hero-bg.jpg">
    <meta property="og:site_name" content="1xreviews">
    <meta property="og:locale" content="en_US">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="1xreviews - Get Paid for Writing Reviews">
    <meta name="twitter:description" content="Join 1xreviews and earn money by writing honest reviews. Get paid for your opinions on products and services.">
    <meta name="twitter:image" content="https://1xreviews.com/assets/images/hero-bg.jpg">
    <meta name="twitter:site" content="@1xreviews">
    <meta name="twitter:creator" content="@1xreviews">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="icon" type="image/png" href="/favicon.png">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" as="style">
    <link rel="preload" href="assets/styles.css" as="style">
    
    <!-- External Resources -->
    <script src="https://accounts.google.com/gsi/client" async defer></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="assets/styles.css" rel="stylesheet">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "1xreviews",
        "url": "https://1xreviews.com",
        "logo": "https://1xreviews.com/assets/images/logo.png",
        "description": "Get paid for writing honest reviews. Join 1xreviews and earn money online by sharing your opinions on products and services.",
        "sameAs": [
            "https://facebook.com/1xreviews",
            "https://twitter.com/1xreviews",
            "https://instagram.com/1xreviews"
        ],
        "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "email": "<EMAIL>"
        }
    }
    </script>

    <!-- Additional SEO Meta Tags -->
    <meta name="googlebot" content="index, follow">
    <meta name="bingbot" content="index, follow">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://1xreviews.com/">
    
    <!-- Language and Region -->
    <meta name="language" content="English">
    <meta name="geo.region" content="US">
    <meta name="geo.placename" content="United States">
    
    <!-- Business Information -->
    <meta name="business:contact_data:street_address" content="123 Review Street">
    <meta name="business:contact_data:locality" content="Review City">
    <meta name="business:contact_data:postal_code" content="12345">
    <meta name="business:contact_data:country_name" content="United States">
    <meta name="business:contact_data:phone_number" content="******-REVIEWS">
    <meta name="business:contact_data:email" content="<EMAIL>">
    
    <!-- Social Media Verification -->
    <meta name="google-site-verification" content="your-google-verification-code">
    <meta name="msvalidate.01" content="your-bing-verification-code">
    
    <!-- Additional Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "1xreviews",
        "url": "https://1xreviews.com",
        "description": "Get paid for writing honest reviews. Join 1xreviews and earn money online by sharing your opinions on products and services.",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "https://1xreviews.com/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
    
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Service",
        "name": "1xreviews Review Platform",
        "description": "Authentic review marketplace connecting businesses with real reviewers",
        "provider": {
            "@type": "Organization",
            "name": "1xreviews",
            "url": "https://1xreviews.com"
        },
        "serviceType": "Review Marketplace",
        "areaServed": "Worldwide",
        "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Review Services",
            "itemListElement": [
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Google Reviews"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Trustpilot Reviews"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Yelp Reviews"
                    }
                }
            ]
        }
    }
    </script>
</head>
<body>
    <!-- Modern Minimal Top Navigation (All Devices) -->
    <nav class="modern-top-nav d-flex align-items-center justify-content-center px-4 py-2 fixed-top">
        <div class="d-flex align-items-center gap-4 flex-wrap justify-content-center w-100">
            <a href="#hero" class="nav-logo d-flex align-items-center text-white text-decoration-none fw-bold fs-4">
                <i class="fas fa-star me-2"></i>1xreviews
            </a>
            <a href="#hero" class="nav-link-minimal text-white text-decoration-none px-3 py-2">Home</a>
            <a href="#about" class="nav-link-minimal text-white text-decoration-none px-3 py-2">About</a>
            <!-- How to Earn Money Dropdown -->
            <div class="dropdown">
                <a href="#" class="nav-link-minimal text-white text-decoration-none px-3 py-2 dropdown-toggle" data-bs-toggle="dropdown">
                    How to Earn Money
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#top-earners">Top Earners</a></li>
                    <li><a class="dropdown-item" href="#recent-opportunities">Available Reviews</a></li>
                    <li><a class="dropdown-item" href="#services">Review Platforms</a></li>
                </ul>
            </div>
            <!-- How to Get Reviews Dropdown -->
            <div class="dropdown">
                <a href="#" class="nav-link-minimal text-white text-decoration-none px-3 py-2 dropdown-toggle" data-bs-toggle="dropdown">
                    How to Get Reviews
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#services">Our Services</a></li>
                    <li><a class="dropdown-item" href="#benefits">Why Choose Us</a></li>
                    <li><a class="dropdown-item" href="#pricing">Pricing</a></li>
                </ul>
            </div>
            <!-- Support Dropdown with FAQ -->
            <div class="dropdown">
                <a href="#" class="nav-link-minimal text-white text-decoration-none px-3 py-2 dropdown-toggle" data-bs-toggle="dropdown">
                    Support
                </a>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#support">Support</a></li>
                    <li><a class="dropdown-item" href="#faq">FAQ</a></li>
                </ul>
            </div>
        </div>
        <div class="d-flex align-items-center gap-2 flex-wrap justify-content-center">
            <button class="btn btn-outline-light rounded-pill px-4 py-2 fw-semibold" onclick="showLoginModal('reviewer')">
                <i class="fas fa-user-tie me-2"></i>Login as Worker
            </button>
            <button class="btn btn-light rounded-pill px-4 py-2 fw-semibold" onclick="showLoginModal('customer')">
                <i class="fas fa-star me-2"></i>I Want Review
            </button>
        </div>
    </nav>
    <style>
    .modern-top-nav {
        background: transparent;
        z-index: 1051;
        min-height: 60px;
    }
    .nav-logo {
        font-size: 1.5rem;
        font-weight: 700;
        letter-spacing: -0.5px;
        color: #fff !important;
    }
    .nav-link-minimal {
        font-size: 1rem;
        font-weight: 500;
        color: #fff !important;
        opacity: 0.85;
        border-radius: 8px;
        transition: background 0.2s, color 0.2s;
    }
    .nav-link-minimal:hover, .nav-link-minimal.active {
        background: rgba(255,255,255,0.08);
        color: #fff !important;
        opacity: 1;
    }
    .modern-top-nav .dropdown-menu {
        background: rgba(30, 41, 59, 0.95);
        backdrop-filter: blur(16px);
        border: 1px solid rgba(255,255,255,0.1);
        border-radius: 12px;
        margin-top: 0.5rem;
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }
    .modern-top-nav .dropdown-item {
        color: #fff;
        padding: 0.75rem 1rem;
        border-radius: 8px;
        margin: 0.25rem;
        transition: all 0.2s;
    }
    .modern-top-nav .dropdown-item:hover {
        background: rgba(255,255,255,0.1);
        color: #fff;
        transform: translateX(4px);
    }
    .modern-top-nav .btn {
        font-size: 1rem;
        font-weight: 600;
        border-width: 2px;
        box-shadow: none;
        transition: all 0.2s;
    }
    .modern-top-nav .btn-light {
        background: #fff;
        color: #1e293b;
        border: none;
    }
    .modern-top-nav .btn-light:hover {
        background: #f3f4f6;
        color: #1e293b;
        transform: translateY(-2px);
    }
    .modern-top-nav .btn-outline-light {
        border-color: #fff;
        color: #fff;
        background: transparent;
    }
    .modern-top-nav .btn-outline-light:hover {
        background: rgba(255,255,255,0.12);
        color: #fff;
        transform: translateY(-2px);
    }
    @media (max-width: 767.98px) {
        .modern-top-nav { display: none !important; }
    }
    </style>
    <!-- Bottom Tab Navigation for Mobile -->
    <nav class="mobile-bottom-nav d-md-none d-lg-none d-xl-none">
        <a href="#hero" class="mobile-tab">
            <i class="fas fa-home"></i>
            <span>Home</span>
        </a>
        <a href="#services" class="mobile-tab">
            <i class="fas fa-th-large"></i>
            <span>Services</span>
        </a>
        <a href="#top-earners" class="mobile-tab">
            <i class="fas fa-trophy"></i>
            <span>Earn</span>
        </a>
        <a href="#benefits" class="mobile-tab">
            <i class="fas fa-heart"></i>
            <span>Benefits</span>
        </a>
        <a href="#loginModal" class="mobile-tab" data-bs-toggle="modal">
            <i class="fas fa-user"></i>
            <span>Account</span>
        </a>
    </nav>
    <style>
    .mobile-bottom-nav {
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1050;
        background: #fff;
        border-top: 1px solid #e5e7eb;
        display: flex;
        justify-content: space-around;
        align-items: center;
        height: 60px;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.04);
    }
    .mobile-tab {
        flex: 1 1 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #374151;
        text-decoration: none;
        font-size: 0.85rem;
        font-weight: 500;
        padding: 0.25rem 0;
        transition: color 0.2s;
    }
    .mobile-tab i {
        font-size: 1.25rem;
        margin-bottom: 2px;
    }
    .mobile-tab:active, .mobile-tab:focus, .mobile-tab.active {
        color: #1877f2;
    }
    @media (min-width: 768px) {
        .mobile-bottom-nav { display: none !important; }
    }
    </style>
    <script>
    // Toggle .scrolled class on nav when scrolling
    window.addEventListener('scroll', function() {
        var nav = document.querySelector('.modern-top-nav');
        if (window.scrollY > 10) {
            nav.classList.add('scrolled');
        } else {
            nav.classList.remove('scrolled');
        }
    });
    </script>
</body>
</html> 