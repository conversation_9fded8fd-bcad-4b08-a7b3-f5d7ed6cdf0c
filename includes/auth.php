<?php
// includes/auth.php

// Start session only if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once __DIR__ . '/db.php';

// CSRF Protection
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Rate Limiting
function check_rate_limit($action, $max_attempts = 5, $time_window = 300) {
    $ip = $_SERVER['REMOTE_ADDR'];
    $key = "rate_limit_{$action}_{$ip}";
    
    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = ['attempts' => 0, 'first_attempt' => time()];
    }
    
    $rate_data = $_SESSION[$key];
    
    // Reset if time window has passed
    if (time() - $rate_data['first_attempt'] > $time_window) {
        $_SESSION[$key] = ['attempts' => 1, 'first_attempt' => time()];
        return true;
    }
    
    // Check if limit exceeded
    if ($rate_data['attempts'] >= $max_attempts) {
        return false;
    }
    
    // Increment attempts
    $_SESSION[$key]['attempts']++;
    return true;
}

// Input Validation and Sanitization
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

function validate_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function validate_url($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

function login($email, $password) {
    global $pdo;
    $stmt = $pdo->prepare('SELECT * FROM users WHERE email = ? LIMIT 1');
    $stmt->execute([$email]);
    $user = $stmt->fetch();
    if ($user && password_verify($password, $user['password'])) {
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['role'] = $user['role'];
        return true;
    }
    return false;
}

function logout() {
    unset($_SESSION['user_data']);
    session_unset();
    session_destroy();
}

function is_logged_in() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function current_user() {
    global $pdo;
    if (!is_logged_in()) return null;
    if (isset($_SESSION['user_data']) && is_array($_SESSION['user_data']) && isset($_SESSION['user_data']['id'])) {
        return $_SESSION['user_data'];
    }
    $stmt = $pdo->prepare('SELECT * FROM users WHERE id = ? LIMIT 1');
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    if ($user) {
        $_SESSION['user_data'] = $user; // cache in session
    }
    return $user;
}

function require_login() {
    if (!is_logged_in()) {
        header('Location: /login.php');
        exit;
    }
}

function require_role($role) {
    require_login();
    if ($_SESSION['role'] !== $role && $_SESSION['role'] !== 'admin') {
        header('Location: /dashboard/' . $_SESSION['role'] . '.php');
        exit;
    }
}

function redirect_dashboard_by_role() {
    if (!is_logged_in()) {
        header('Location: /index.php');
        exit;
    }
    $role = $_SESSION['role'] ?? null;
    if ($role === 'admin') {
        header('Location: /dashboard/admin.php');
    } elseif ($role === 'reviewer') {
        header('Location: /dashboard/reviewer.php');
    } else {
        header('Location: /dashboard/customer.php');
    }
    exit;
}

// Security Headers
function set_security_headers() {
    // Only set headers if they haven't been sent and this isn't an AJAX request
    if (!headers_sent() && !isset($_SERVER['HTTP_X_REQUESTED_WITH'])) {
        header('X-Content-Type-Options: nosniff');
        header('X-Frame-Options: DENY');
        header('X-XSS-Protection: 1; mode=block');
        header('Referrer-Policy: strict-origin-when-cross-origin');
        header("Content-Security-Policy: ".
            "default-src 'self'; ".
            "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://www.google.com https://accounts.google.com; ".
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com https://cdnjs.cloudflare.com https://accounts.google.com/gsi/style; ".
            "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; ".
            "img-src 'self' data: https:; ".
            "connect-src 'self' https://accounts.google.com; ".
            "frame-src 'self' https://accounts.google.com https://www.google.com; ".
            "child-src 'self' https://accounts.google.com https://www.google.com;"
        );
    }
}

// Initialize security headers only for non-AJAX requests
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strpos($_SERVER['REQUEST_URI'], 'google_login.php') === false) {
    set_security_headers();
}
